﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Graph.Models;
using TeyaUIModels.Model;
using TeyaUIViewModels.TeyaUIViewModelResources;

namespace TeyaUIViewModels.ViewModel
{
    public class PaymentService : IPaymentService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _EncounterNotes;
        private readonly ITokenService _tokenService;

        public PaymentService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _EncounterNotes = Environment.GetEnvironmentVariable("BillingURL");
            _tokenService = tokenService;
        }


        public async Task<PaymentsData> GetPaymentByIdAsync(Guid PaymentId, Guid? OrgId, bool Subscription)
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                var apiUrl = $"{_EncounterNotes}/api/Payments/{PaymentId}/{OrgId}/{Subscription}";

                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (!response.IsSuccessStatusCode)
                {
                    throw new HttpRequestException($"Error fetching Payment: {response.StatusCode}");
                }

                var responseData = await response.Content.ReadAsStringAsync();

                if (string.IsNullOrWhiteSpace(responseData))
                    return null;

                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                return JsonSerializer.Deserialize<PaymentsData>(responseData, options);
            }
            catch (Exception ex)
            {
                throw new Exception($"An error occurred while retrieving the Payment: {ex.Message}", ex);
            }
        }
        public async Task<List<PaymentsData>> GetAllByOrgIdAsync(Guid? OrgId, bool Subscription)
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                var apiUrl = $"{_EncounterNotes}/api/Payments/{OrgId}/{Subscription}";

                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (!response.IsSuccessStatusCode)
                {
                    throw new HttpRequestException($"Error fetching Payment: {response.StatusCode}");
                }

                var responseData = await response.Content.ReadAsStringAsync();

                if (string.IsNullOrWhiteSpace(responseData))
                    return null;

                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                return JsonSerializer.Deserialize<List<PaymentsData>>(responseData, options) ?? new List<PaymentsData>();
            }
            catch (Exception ex)
            {
                throw new Exception($"An error occurred while retrieving the Payment: {ex.Message}", ex);
            }
        }


        public async Task<List<PaymentsData>> GetPaymentsByPatientIdAsync(Guid id, Guid? OrgID, bool Subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_EncounterNotes}/api/Payments/PatientId/{id}/{OrgID}/{Subscription}";

                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (!response.IsSuccessStatusCode)
                {
                    throw new HttpRequestException($"Error fetching Payments: {response.StatusCode}");
                }

                var responseData = await response.Content.ReadAsStringAsync();

                if (string.IsNullOrWhiteSpace(responseData) || responseData == "[]")
                    return new List<PaymentsData>();

                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                return JsonSerializer.Deserialize<List<PaymentsData>>(responseData, options) ?? new List<PaymentsData>();
            }
            catch (Exception ex)
            {
                throw new Exception($"An error occurred while retrieving Payments: {ex.Message}", ex);
            }
        }

        public async Task<List<PaymentsData>> GetPaymentsByPCPIdAsync(Guid pcpId, Guid? OrgID, bool Subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_EncounterNotes}/api/Payments/ByPCP/{pcpId}/{OrgID}/{Subscription}";

                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (!response.IsSuccessStatusCode)
                {
                    throw new HttpRequestException($"Error fetching Payments: {response.StatusCode}");
                }

                var responseData = await response.Content.ReadAsStringAsync();

                if (string.IsNullOrWhiteSpace(responseData) || responseData == "[]")
                    return new List<PaymentsData>();

                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                return JsonSerializer.Deserialize<List<PaymentsData>>(responseData, options) ?? new List<PaymentsData>();
            }
            catch (Exception ex)
            {
                throw new Exception($"An error occurred while retrieving Payments: {ex.Message}", ex);
            }
        }

        public async Task<HttpResponseMessage> SavePaymentAsync(PaymentsData updatedPayment, Guid? OrgID, bool Subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_EncounterNotes}/api/Payments/{updatedPayment.PaymentId}/{OrgID}/{Subscription}";

                var bodyContent = JsonSerializer.Serialize(updatedPayment, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
                });

                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
                var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                requestMessage.Content = content;

                var response = await _httpClient.SendAsync(requestMessage);

                return response;
            }
            catch (Exception ex)
            {
                throw new Exception($"An error occurred while Updating Payment: {ex.Message}", ex);
                throw;
            }
        }

        public async Task<HttpResponseMessage> UploadPaymentAsync(PaymentsData updatedPayment, Guid? OrgID, bool Subscription)
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                var apiUrl = $"{_EncounterNotes}/api/Payments/{OrgID}/{Subscription}";

                var bodyContent = JsonSerializer.Serialize(new List<PaymentsData> { updatedPayment }, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
                });

                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                requestMessage.Content = content;

                var response = await _httpClient.SendAsync(requestMessage);
                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"Error response: {response.StatusCode}, Content: {errorContent}");
                }
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception($"An error occurred while Updating Payment: {ex.Message}", ex);
                throw;
            }
        }
    }
}


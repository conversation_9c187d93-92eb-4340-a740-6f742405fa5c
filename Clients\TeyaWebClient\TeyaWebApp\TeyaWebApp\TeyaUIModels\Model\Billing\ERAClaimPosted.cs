﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model.Billing
{
    public class ERAClaimPosted
    {
        public Guid ClaimPostedId { get; set; }
        public string? ClaimNo { get; set; } = string.Empty;
        public DateTime ServiceDate { get; set; }
        public string? PatientName { get; set; } = string.Empty;
        public decimal Billed { get; set; }
        public decimal Allowed { get; set; }
        public decimal Deduct { get; set; }
        public decimal Coins { get; set; }
        public decimal Copay { get; set; }
        public decimal Paid { get; set; }
        public decimal Adjustment { get; set; }
        public decimal Withhold { get; set; }
        public string? Code { get; set; } = string.Empty;
        public Guid ERAId { get; set; }
    }
}

using System.Text.Json.Serialization;

namespace TeyaUIModels.Model
{
    public class InstitutionalClaim : IModel
    {
        public Guid ID { get; set; }
        public Guid OrganizationID { get; set; }
        public bool Subscription { get; set; }
        public bool BillToPatient { get; set; }
        public List<InsuranceItem> InsuranceItems { get; set; } = new();
        public List<PaymentItem> PaymentItems { get; set; } = new();

        // Navigation properties for selected items - these should reference items in the collections
        public Guid? SelectedInsuranceId { get; set; }
        public Guid? SelectedPaymentId { get; set; }

        [JsonIgnore]
        public InsuranceItem? SelectedInsurance { get; set; }
        [JsonIgnore]
        public PaymentItem? SelectedPayment { get; set; }

        public bool IsActive { get; set; } = true;

        // Basic facility information
        public string? FacilityName { get; set; }
        public string? FacilityAddress { get; set; }
        public string? FacilityPhone { get; set; }
        public string? LocalUseField1 { get; set; }
        public string? LocalUseField2 { get; set; }
        public string? LocalUseField3 { get; set; }

        // Control information
        public string? PatientControlNo { get; set; }
        public string? BillType { get; set; }
        public string? FedTaxNo { get; set; }

        // Statement period
        public DateTime? StatementFromDate { get; set; } = DateTime.Today;
        public DateTime? StatementThroughDate { get; set; }
        public int? CoveredDays { get; set; }
        public int? NonCoveredDays { get; set; }
        public int? CoinsuranceDays { get; set; }

        // Patient information
        public string? PatientName { get; set; }
        public string? PatientAddress { get; set; }
        public DateTime? BirthDate { get; set; }
        public string? Sex { get; set; }
        public string? MaritalStatus { get; set; }

        // Admission information
        public DateTime? AdmissionDate { get; set; }
        public string? AdmissionHour { get; set; }
        public string? AdmissionType { get; set; }
        public string? AdmissionSource { get; set; }
        public string? DischargeHour { get; set; }
        public string? Status { get; set; }
        public string? MedicalRecordNo { get; set; }

        // Condition codes
        public string? ConditionCode24 { get; set; }
        public string? ConditionCode25 { get; set; }
        public string? ConditionCode26 { get; set; }
        public string? ConditionCode27 { get; set; }
        public string? ConditionCode28 { get; set; }
        public string? ConditionCode29 { get; set; }
        public string? ConditionCode30 { get; set; }
        public string? AccidentState { get; set; }
        public string? LocalUseField { get; set; }

        // Additional fields
        public string? AdmDiagCD { get; set; }
        public string? ECode { get; set; }
        public string? LU1 { get; set; }
        public string? LU2 { get; set; }
        public string? ICD81CC_A { get; set; }
        public string? ICD81CC_B { get; set; }
        public string? ICD81CC_C { get; set; }
        public string? ICD81CC_D { get; set; }

        // Provider information
        public string? PrincipalProcedureCode { get; set; }
        public DateTime? PrincipalProcedureDate { get; set; } = DateTime.Today;
        public string? AttendingPhysID { get; set; }
        public string? ProviderRepresentative { get; set; }
        public DateTime? ProviderDate { get; set; } = DateTime.Today;

        // Remarks
        public string? Remark_A { get; set; }
        public string? Remark_B { get; set; }
        public string? Remark_C { get; set; }
    }

    public class OccurrenceItem
    {
        public string Code { get; set; } = string.Empty;
        public DateTime? Date { get; set; }
    }

    public class ValueCodeItem
    {
        public string Code { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public decimal? Amount { get; set; }
    }

    public class ServiceDetailItem
    {
        public string Description { get; set; } = string.Empty;
        public string ServiceCode { get; set; } = string.Empty;
        public string Units { get; set; } = string.Empty;
        public DateTime? Date { get; set; }
        public decimal? Amount { get; set; }
    }

    public class ICD9CodeItem
    {
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
    }

    public class LUFieldItem
    {
        public string Field { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
    }
}

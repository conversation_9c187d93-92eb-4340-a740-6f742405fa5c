﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class Office_visit_members : IModel
    {
        public Guid PatientId { get; set; }
        public string? UserName { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string? SexualOrientation { get; set; }
        public string? MRN { get; set; }
    }
}


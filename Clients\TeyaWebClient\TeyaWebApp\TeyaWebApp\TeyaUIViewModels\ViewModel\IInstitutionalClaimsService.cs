﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IInstitutionalClaimsService
    {
        Task<IEnumerable<InstitutionalClaim>> GetAllInstitutionalClaimAsync(Guid orgId, bool subscription);
        Task<InstitutionalClaim> GetInstitutionalClaimByIdAsync(Guid id, Guid orgId, bool subscription);
        Task AddInstitutionalClaimsAsync(InstitutionalClaim institutionalClaims, Guid orgId, bool subscription);
        Task UpdateInstitutionalClaimsAsync(InstitutionalClaim institutionalClaims, Guid id, Guid orgId, bool subscription);
        Task DeleteInstitutionalClaimsAsync(Guid id, Guid orgId, bool subscription);
    }
}
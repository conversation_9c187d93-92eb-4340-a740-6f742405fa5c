@using MudBlazor
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <div class="add-comment-dialog">
            <div class="dialog-header">
                <MudIcon Icon="@Icons.Material.Filled.Comment" Class="header-icon" />
                <MudText Typo="Typo.h6">Add Comment</MudText>
            </div>
            
            <div class="commenting-section">
                <MudText Typo="Typo.body2" Color="Color.Secondary" Class="mb-3">
                    Commenting on: <strong>@SectionName</strong>
                </MudText>
                
                <div class="comment-form">
                    <MudText Typo="Typo.subtitle2" Class="mb-2">Comment</MudText>
                    <MudTextField @bind-Value="CommentText"
                                  Label="Enter your comment..."
                                  Variant="Variant.Outlined"
                                  Lines="4"
                                  MaxLength="500"
                                  Counter="500"
                                  HelperText="Provide specific feedback or suggestions for improvement."
                                  Class="comment-input" />
                </div>
            </div>
        </div>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel" 
                   Variant="Variant.Outlined" 
                   Color="Color.Secondary"
                   Class="cancel-btn">
            Cancel
        </MudButton>
        <MudButton OnClick="AddComment" 
                   Variant="Variant.Filled" 
                   Color="Color.Primary"
                   Disabled="string.IsNullOrWhiteSpace(CommentText)"
                   Class="add-comment-btn">
            Add Comment
        </MudButton>
    </DialogActions>
</MudDialog>

<style>
    .add-comment-dialog {
        min-width: 500px;
        padding: 16px 0;
    }

    .dialog-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 24px;
        color: #1976d2;
    }

    .header-icon {
        color: #1976d2;
    }

    .commenting-section {
        background: #f8f9fa;
        padding: 16px;
        border-radius: 8px;
        border-left: 4px solid #1976d2;
    }

    .comment-form {
        margin-top: 16px;
    }

    .comment-input {
        background: white;
        border-radius: 8px;
    }

    .cancel-btn {
        min-width: 80px;
    }

    .add-comment-btn {
        min-width: 120px;
    }

    @media (max-width: 600px) {
        .add-comment-dialog {
            min-width: 300px;
        }
    }
</style>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; }
    [Parameter] public string SectionName { get; set; } = string.Empty;
    [Parameter] public EventCallback<string> OnCommentAdded { get; set; }

    private string CommentText { get; set; } = string.Empty;

    private void Cancel()
    {
        MudDialog.Cancel();
    }

    private async Task AddComment()
    {
        if (string.IsNullOrWhiteSpace(CommentText))
        {
            Snackbar.Add("Please enter a comment", Severity.Warning);
            return;
        }

        try
        {
            await OnCommentAdded.InvokeAsync(CommentText);
            Snackbar.Add("Comment added successfully", Severity.Success);
            MudDialog.Close(DialogResult.Ok(CommentText));
        }
        catch (Exception ex)
        {
            Snackbar.Add("Error adding comment", Severity.Error);
        }
    }
}

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;

namespace TeyaUIViewModels.ViewModel
{
    public class SignatureService : ISignatureService
    {
        private readonly HttpClient _httpClient;
        private readonly ITokenService _tokenService;
        private readonly ILogger<SignatureService> _logger;
        private readonly IStringLocalizer<SignatureService> _localizer;
        private readonly string _signatureServiceUrl;

        public SignatureService(
            HttpClient httpClient,
            ITokenService tokenService,
            ILogger<SignatureService> logger,
            IStringLocalizer<SignatureService> localizer,
            IConfiguration configuration)
        {
            _httpClient = httpClient;
            _tokenService = tokenService;
            _logger = logger;
            _localizer = localizer;
            _signatureServiceUrl = Environment.GetEnvironmentVariable("SignatureServiceURL") ?? "https://localhost:7001";
        }

        public async Task<DocumentSignature> CreateSignatureAsync(DocumentSignature signature, Guid orgId, bool subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_signatureServiceUrl}/api/Signatures/{orgId}/{subscription}";

                var json = JsonSerializer.Serialize(signature, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var request = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = content
                };
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(request);
                response.EnsureSuccessStatusCode();

                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<DocumentSignature>(responseContent, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating signature");
                throw;
            }
        }

        public async Task<List<DocumentSignature>> GetSignaturesByRecordIdAsync(Guid recordId, Guid orgId, bool subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_signatureServiceUrl}/api/Signatures/record/{recordId}/{orgId}/{subscription}";

                var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(request);
                response.EnsureSuccessStatusCode();

                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<DocumentSignature>>(responseContent, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                }) ?? new List<DocumentSignature>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting signatures for record {RecordId}", recordId);
                return new List<DocumentSignature>();
            }
        }

        public async Task<DocumentSignature> GetSignatureByIdAsync(Guid signatureId, Guid orgId, bool subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_signatureServiceUrl}/api/Signatures/{signatureId}/{orgId}/{subscription}";

                var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(request);
                response.EnsureSuccessStatusCode();

                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<DocumentSignature>(responseContent, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting signature {SignatureId}", signatureId);
                throw;
            }
        }

        public async Task<bool> HasRecordBeenSignedAsync(Guid recordId, Guid orgId, bool subscription)
        {
            var signatures = await GetSignaturesByRecordIdAsync(recordId, orgId, subscription);
            return signatures.Any(s => s.Type == SignatureType.Primary && s.IsActive);
        }

        public async Task<bool> HasRecordBeenCoSignedAsync(Guid recordId, Guid orgId, bool subscription)
        {
            var signatures = await GetSignaturesByRecordIdAsync(recordId, orgId, subscription);
            return signatures.Any(s => s.Type == SignatureType.CoSign && s.IsActive);
        }

        public async Task<SignatureRequest> CreateSignatureRequestAsync(SignatureRequest request, Guid orgId, bool subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_signatureServiceUrl}/api/SignatureRequests/{orgId}/{subscription}";

                var json = JsonSerializer.Serialize(request, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var httpRequest = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = content
                };
                httpRequest.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(httpRequest);
                response.EnsureSuccessStatusCode();

                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<SignatureRequest>(responseContent, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating signature request");
                throw;
            }
        }

        public async Task<SignatureRequest> UpdateSignatureRequestAsync(SignatureRequest request, Guid orgId, bool subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_signatureServiceUrl}/api/SignatureRequests/{request.Id}/{orgId}/{subscription}";

                var json = JsonSerializer.Serialize(request, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var httpRequest = new HttpRequestMessage(HttpMethod.Put, apiUrl)
                {
                    Content = content
                };
                httpRequest.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(httpRequest);
                response.EnsureSuccessStatusCode();

                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<SignatureRequest>(responseContent, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating signature request");
                throw;
            }
        }

        public async Task<List<SignatureRequest>> GetIncomingRequestsAsync(Guid reviewerId, Guid orgId, bool subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_signatureServiceUrl}/api/SignatureRequests/incoming/{reviewerId}/{orgId}/{subscription}";

                var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(request);
                response.EnsureSuccessStatusCode();

                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<SignatureRequest>>(responseContent, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                }) ?? new List<SignatureRequest>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting incoming requests for reviewer {ReviewerId}", reviewerId);
                return new List<SignatureRequest>();
            }
        }

        public async Task<List<SignatureRequest>> GetOutgoingRequestsAsync(Guid requesterId, Guid orgId, bool subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_signatureServiceUrl}/api/SignatureRequests/outgoing/{requesterId}/{orgId}/{subscription}";

                var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(request);
                response.EnsureSuccessStatusCode();

                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<SignatureRequest>>(responseContent, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                }) ?? new List<SignatureRequest>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting outgoing requests for requester {RequesterId}", requesterId);
                return new List<SignatureRequest>();
            }
        }

        public async Task<SignatureRequest> GetSignatureRequestByIdAsync(Guid requestId, Guid orgId, bool subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_signatureServiceUrl}/api/SignatureRequests/{requestId}/{orgId}/{subscription}";

                var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(request);
                response.EnsureSuccessStatusCode();

                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<SignatureRequest>(responseContent, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting signature request {RequestId}", requestId);
                throw;
            }
        }

        public async Task<bool> ApproveSignatureRequestAsync(Guid requestId, string reviewerComments, Guid orgId, bool subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_signatureServiceUrl}/api/SignatureRequests/{requestId}/approve/{orgId}/{subscription}";

                var requestData = new { ReviewerComments = reviewerComments };
                var json = JsonSerializer.Serialize(requestData, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var httpRequest = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = content
                };
                httpRequest.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(httpRequest);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error approving signature request {RequestId}", requestId);
                return false;
            }
        }

        public async Task<bool> RejectSignatureRequestAsync(Guid requestId, string reviewerComments, Guid orgId, bool subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_signatureServiceUrl}/api/SignatureRequests/{requestId}/reject/{orgId}/{subscription}";

                var requestData = new { ReviewerComments = reviewerComments };
                var json = JsonSerializer.Serialize(requestData, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var httpRequest = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = content
                };
                httpRequest.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(httpRequest);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rejecting signature request {RequestId}", requestId);
                return false;
            }
        }

        public async Task<bool> RequestChangesAsync(Guid requestId, string reviewerComments, Guid orgId, bool subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_signatureServiceUrl}/api/SignatureRequests/{requestId}/request-changes/{orgId}/{subscription}";

                var requestData = new { ReviewerComments = reviewerComments };
                var json = JsonSerializer.Serialize(requestData, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var httpRequest = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = content
                };
                httpRequest.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(httpRequest);
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error requesting changes for signature request {RequestId}", requestId);
                return false;
            }
        }

        public async Task<bool> CanLockRecordAsync(Guid recordId, Guid orgId, bool subscription)
        {
            // Record can be locked if it's signed and has no pending cosign requests
            var isSigned = await HasRecordBeenSignedAsync(recordId, orgId, subscription);
            var hasPendingRequest = await HasPendingCoSignRequestAsync(recordId, orgId, subscription);
            
            return isSigned && !hasPendingRequest;
        }

        public async Task<bool> HasPendingCoSignRequestAsync(Guid recordId, Guid orgId, bool subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_signatureServiceUrl}/api/SignatureRequests/pending/{recordId}/{orgId}/{subscription}";

                var request = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(request);
                response.EnsureSuccessStatusCode();

                var responseContent = await response.Content.ReadAsStringAsync();
                var hasPending = JsonSerializer.Deserialize<bool>(responseContent);
                return hasPending;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking pending cosign requests for record {RecordId}", recordId);
                return false;
            }
        }
    }
}

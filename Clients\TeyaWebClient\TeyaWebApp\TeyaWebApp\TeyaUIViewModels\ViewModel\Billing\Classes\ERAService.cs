﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using TeyaUIModels.Model.Billing;
using TeyaUIViewModels.TeyaUIViewModelResources;
using TeyaUIViewModels.ViewModel.Billing.Interfaces;

namespace TeyaUIViewModels.ViewModel.Billing.Classes
{
    public class ERAService : IERAService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _Billing;
        private readonly ITokenService _tokenService;

        public ERAService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _Billing = Environment.GetEnvironmentVariable("BillingURL");
            _tokenService = tokenService;
        }

        /// <summary>
        /// Get all ERA, both active and inactive
        /// </summary>
        public async Task<List<CompleteERA>> GetERAByIdAsync(Guid id, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_Billing}/api/ERA/{id}/{OrgID}/{Subscription}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = _tokenService.AccessToken;
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<CompleteERA>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["AddressRetrievalFailure"]);
            }
        }

        /// <summary>
        /// Add New list of ERA
        /// </summary>
        public async Task AddERAAsync(List<CompleteERA> completeERA, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_Billing}/api/ERA/Add/{OrgID}/{Subscription}";
            var accessToken = _tokenService.AccessToken;
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(completeERA);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            var response = await _httpClient.SendAsync(requestMessage);
            {
                if (response.IsSuccessStatusCode)
                {
                    var responseBody = await response.Content.ReadAsStringAsync();
                }
                else
                {
                    var error = await response.Content.ReadAsStringAsync();

                    throw new HttpRequestException(_localizer["TasksRetrievalFailure"]);
                }
            }
        }

        /// <summary>
        /// Delete ERA
        /// </summary>
        public async Task DeleteERAAsync(CompleteERA ERA, Guid id, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_Billing}/api/ERA/{id}/{OrgID}/{Subscription}";
            var accessToken = _tokenService.AccessToken;
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(ERA);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                var responseBody = await response.Content.ReadAsStringAsync();
            }
            else
            {
                var error = response.Content.ReadAsStringAsync();

                throw new HttpRequestException(_localizer["TasksRetrievalFailure"]);
            }
        }

        /// <summary>
        /// Update single ERA
        /// </summary>
        /// <param name = "ERA" ></ param >
        /// < returns ></ returns >
        public async Task UpdateERAAsync(CompleteERA ERA, Guid? OrgID, bool Subscription)
        {
            var accessToken = _tokenService.AccessToken;
            var apiUrl = $"{_Billing}/api/ERA/{ERA.Id}/{OrgID}/{Subscription}";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(ERA);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            requestMessage.Content = content;

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }
        public async Task<List<CompleteERA>> GetAllERAAsync(Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_Billing}/api/ERA/{OrgID}/{Subscription}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = _tokenService.AccessToken;
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<CompleteERA>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["AddressRetrievalFailure"]);
            }
        }

    }

}


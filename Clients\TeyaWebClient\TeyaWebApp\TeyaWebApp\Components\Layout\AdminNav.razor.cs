using BusinessLayer.Services;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using MudBlazor;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Services;
using TeyaWebApp.ViewModel;

namespace TeyaWebApp.Components.Layout
{
    public partial class AdminNav : ComponentBase
    {
        private readonly string Chart = "/chart";
        private readonly string Appointments = "/appointments";
        //private readonly string OrderSets = "/ordersets";
        private readonly string Message = "/message";
        private readonly string Practice = "/practice";
        private readonly string Document = "/document";
        private readonly string Patients = "/patients";
        private readonly string PlanBilling = "/planbilling";
        private readonly string Staff = "/staff";
        private readonly string LicenseActivation = "/licenseactivation";
        private readonly string License = "/license";
        private readonly string ProductFeatureSettings = "/productfeaturesettings";
        private readonly string Security = "/security";
        private readonly string UserManagement = "/usermanagement";
        private readonly string Templates = "/templates";
        private readonly string Config = "/config";
        private readonly string About = "/about";

        private readonly string ClaimsLookup = "/claimslookup";
        private readonly string BillingEncounters = "/billingencounters";
        private readonly string InstitutionalClaims = "/institutionalclaims";
        private readonly string PatientDentalClaims = "/dentalclaims";
        private readonly string BillingPayments = "/billingPayments";
        private readonly string PatientProfessionalClaims = "/professionalclaims";
        private readonly string PatientERA = "/era";

        private const string EHR_PRODUCT = "EHR";
        private const string BILLING_PRODUCT = "Billing";
        private const string adminRole = "Admin";
        private const string orgName = "TeyaHealth";

        private List<string> AvailableProducts = new();
        private bool HasMultipleProducts = false;

        private static readonly Dictionary<string, HashSet<string>> ProductPageMappings = new()
        {
            [EHR_PRODUCT] = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
            {
                "/chart", "/appointments", "/message","/practice",
                "/document", "/patients", "/planbilling", "/staff",
                "/licenseactivation", "/license", "/productfeaturesettings", "/security",
                "/usermanagement", "/templates", "/config", "/about"
            },
            [BILLING_PRODUCT] = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
            {
                "/claimslookup","/dentalclaims","/era", "/professionalclaims", "/billingencounters" , "/institutionalclaims"
            }
        };

        public enum MappingSource
        {
            None,
            CustomMappings,
            PreDefinedMappings
        }

        private MappingSource CurrentMappingSource { get; set; } = MappingSource.None;
        private bool ShouldRenderLicenseLink = false;
        private List<ProductOrganizationMapping> UserProductMappings = new();
        private List<Product> AllProducts = new();
        private TeyaUIModels.Model.Organization CurrentOrganization = null;
        private bool HasEHRAccess = false;
        private bool HasBillingAccess = false;

        [Parameter] public bool IsDrawerOpen { get; set; }
        [Inject] private IMemberService MemberService { get; set; } = default!;
        [Inject] private IPageRoleMappingService PageRoleMappingService { get; set; } = default!;
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        [Inject] private IPreDefinedPageRoleMappingService PreDefinedPageRoleMappingService { get; set; } = default!;
        [Inject] private ILogger<AdminNav> Logger { get; set; } = default!;
        [Inject] private RoleMappingState RoleMappingState { get; set; }
        [Inject] private ActiveUser activeUser { get; set; }
        private bool Subscription = false;
        [Inject] private IProductOrganizationMappingService ProductOrganizationMappingService { get; set; }
        [Inject] private IProductService ProductService { get; set; }
        private List<Member> members = new();
        private List<PageRoleMappingData> PageRoleMappings = new();
        private List<PreDefinedPageRoleMappingData> PreDefinedPageRoleMappings = new();

        [Parameter] public string SelectedProduct { get; set; } = "EHR";

        public bool HasEHRProductAccess() => HasEHRAccess;
        public bool HasBillingProductAccess() => HasBillingAccess;
        public bool ShouldShowProductSwitcher() => HasMultipleProducts;
        public string GetSelectedProduct() => SelectedProduct;
        public bool IsEHRSelected() => SelectedProduct == "EHR";
        public bool IsBillingSelected() => SelectedProduct == "Billing";
        private bool _isLoading = true;
        private bool _showNoProductMessage = false;
        [Inject] private UserContext usercontext { get; set; }
        private bool ActiveUserSubscription { get; set; }
        private Guid ActiveUserOrgID { get; set; }

        protected override async Task OnInitializedAsync()
        {
            try
            {
                ActiveUserSubscription = usercontext.ActiveUserSubscription;
                ActiveUserOrgID = usercontext.ActiveUserOrganizationID;

                await LoadProductsAndMappingsAsync();
                await LoadPageRoleMappingsAsync();

                var uri = NavigationManager.ToAbsoluteUri(NavigationManager.Uri);
                var queryParams = Microsoft.AspNetCore.WebUtilities.QueryHelpers.ParseQuery(uri.Query);

                if (queryParams.TryGetValue("product", out var productFromQuery))
                {
                    SelectedProduct = productFromQuery.ToString();
                }

                await InitializeProductSwitcher();

                RoleMappingState.OnChange += async () =>
                {
                    await LoadPageRoleMappingsAsync();
                    await InvokeAsync(StateHasChanged);
                };

                await CheckUserAccess();

                LogNavigationState();
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error during initialization: {ex.Message}");
                _showNoProductMessage = true;
            }
            finally
            {
                _isLoading = false;
                StateHasChanged();
            }
        }

        private async Task LoadProductsAndMappingsAsync()
        {
            try
            {
                AllProducts = await ProductService.GetProductsAsync() ?? new List<Product>();

                if (!string.IsNullOrEmpty(activeUser.OrganizationName))
                {
                    var organizationId = await OrganizationService.GetOrganizationIdByNameAsync(activeUser.OrganizationName);
                    if (organizationId != Guid.Empty)
                    {
                        CurrentOrganization = await OrganizationService.GetOrganizationByIdAsync(organizationId);

                        var allMappings = await ProductOrganizationMappingService.GetAllProductOrganizationMappingsAsync();
                        UserProductMappings = allMappings?.Where(m => m.OrganizationId == organizationId && m.IsActive).ToList()
                                           ?? new List<ProductOrganizationMapping>();

                        await DetermineProductAccess();
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error loading products and mappings: {ex.Message}");
            }
        }

        private async Task DetermineProductAccess()
        {
            try
            {
                var ehrProduct = AllProducts.FirstOrDefault(p => p.Name.Equals(EHR_PRODUCT, StringComparison.OrdinalIgnoreCase));
                var billingProduct = AllProducts.FirstOrDefault(p => p.Name.Equals(BILLING_PRODUCT, StringComparison.OrdinalIgnoreCase));

                if (ehrProduct != null)
                {
                    HasEHRAccess = UserProductMappings.Any(m => m.ProductId == ehrProduct.Id);
                }

                if (billingProduct != null)
                {
                    HasBillingAccess = UserProductMappings.Any(m => m.ProductId == billingProduct.Id);
                }

                if (!HasEHRAccess && !HasBillingAccess)
                {
                    HasEHRAccess = true;
                    Logger.LogInformation("No product mappings found, defaulting to EHR access");
                }

                Logger.LogInformation($"Product Access - EHR: {HasEHRAccess}, Billing: {HasBillingAccess}");
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error determining product access: {ex.Message}");
                HasEHRAccess = true;
            }
        }

        private async Task InitializeProductSwitcher()
        {
            AvailableProducts.Clear();

            if (HasEHRAccess)
            {
                AvailableProducts.Add(EHR_PRODUCT);
            }

            if (HasBillingAccess)
            {
                AvailableProducts.Add(BILLING_PRODUCT);
            }

            HasMultipleProducts = AvailableProducts.Count > 1;

            if (string.IsNullOrEmpty(SelectedProduct) || !AvailableProducts.Contains(SelectedProduct))
            {
                SelectedProduct = AvailableProducts.FirstOrDefault() ?? EHR_PRODUCT;
            }

            Logger.LogInformation($"Available Products: {string.Join(", ", AvailableProducts)}");
            Logger.LogInformation($"Selected Product: {SelectedProduct}");
            Logger.LogInformation($"Has Multiple Products: {HasMultipleProducts}");
        }

        private async Task CheckUserAccess()
        {
            if (activeUser.OrganizationName == orgName)
            {
                ShouldRenderLicenseLink = true;
            }
        }

        private async Task LoadPageRoleMappingsAsync()
        {
            var activeUserOrganizationId = ActiveUserOrgID;
            Subscription = ActiveUserSubscription;

            try
            {
                PageRoleMappings.Clear();
                PreDefinedPageRoleMappings.Clear();
                CurrentMappingSource = MappingSource.None;

                members = await MemberService.GetAllMembersAsync(activeUserOrganizationId, Subscription);
                var matchingMember = members.FirstOrDefault(m => m.Id == Guid.Parse(activeUser.id));

                if (matchingMember == null)
                {
                    Logger.LogWarning($"No matching member found for user ID: {activeUser.id}");
                    return;
                }

                activeUser.role = matchingMember.RoleName;
                Logger.LogInformation($"Loading page role mappings for user: {activeUser.id}, role: {matchingMember.RoleName}");

                bool customMappingsLoaded = false;

                try
                {
                    List<PageRoleMappingData> customMappings = null;

                    if (string.Equals(matchingMember.RoleName, adminRole, StringComparison.OrdinalIgnoreCase))
                    {
                        Logger.LogInformation("Loading all page role mappings for admin user");
                        var allPageRoleMappings = await PageRoleMappingService.GetPageRoleMappingsAsync();
                        customMappings = allPageRoleMappings?.ToList();
                    }
                    else if (matchingMember.RoleID.HasValue)
                    {
                        Logger.LogInformation($"Loading page role mappings for role ID: {matchingMember.RoleID.Value}");
                        var roleSpecificMappings = await PageRoleMappingService.GetPagesByRoleIdAsync(
                            matchingMember.RoleID.Value, activeUserOrganizationId, Subscription);
                        customMappings = roleSpecificMappings?.ToList();
                    }

                    if (customMappings?.Any() == true)
                    {
                        PageRoleMappings = customMappings;
                        CurrentMappingSource = MappingSource.CustomMappings;
                        customMappingsLoaded = true;
                        Logger.LogInformation($"Loaded {PageRoleMappings.Count} custom page role mappings");
                    }
                }
                catch (Exception ex)
                {
                    Logger.LogWarning($"Failed to load custom page role mappings: {ex.Message}");
                }

                if (!customMappingsLoaded)
                {
                    try
                    {
                        Logger.LogInformation($"Loading predefined page role mappings for role: {matchingMember.RoleName}");
                        var preDefinedMappings = await PreDefinedPageRoleMappingService.GetPagesByRoleNameAsync(matchingMember.RoleName);

                        if (preDefinedMappings?.Any() == true)
                        {
                            PreDefinedPageRoleMappings = preDefinedMappings.ToList();
                            CurrentMappingSource = MappingSource.PreDefinedMappings;
                            Logger.LogInformation($"Loaded {PreDefinedPageRoleMappings.Count} predefined page role mappings");
                        }
                        else
                        {
                            Logger.LogWarning($"No predefined mappings found for role: {matchingMember.RoleName}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.LogError($"Failed to load predefined page role mappings: {ex.Message}");
                    }
                }

                if (CurrentMappingSource == MappingSource.None)
                {
                    Logger.LogWarning($"No page role mappings found for user {activeUser.id} with role {matchingMember.RoleName}");
                }
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error during page role mapping data loading: {ex.Message}");
            }
        }

        public void Dispose()
        {
            RoleMappingState.OnChange -= async () => await InvokeAsync(StateHasChanged);
        }

        private bool IsPageAccessible(string pageUrl)
        {
            if (string.IsNullOrEmpty(pageUrl))
            {
                return false;
            }

            if (!IsPageForSelectedProduct(pageUrl))
            {
                return false;
            }

            if (CurrentMappingSource == MappingSource.CustomMappings && PageRoleMappings?.Any() == true)
            {
                bool hasAccess = PageRoleMappings.Any(p =>
                    string.Equals(p.PagePath, pageUrl, StringComparison.OrdinalIgnoreCase));

                Logger.LogDebug($"Custom mapping check for {pageUrl}: {hasAccess}");
                return hasAccess;
            }

            if (CurrentMappingSource == MappingSource.PreDefinedMappings && PreDefinedPageRoleMappings?.Any() == true)
            {
                bool hasAccess = PreDefinedPageRoleMappings.Any(p =>
                    string.Equals(p.PagePath, pageUrl, StringComparison.OrdinalIgnoreCase));

                Logger.LogDebug($"Predefined mapping check for {pageUrl}: {hasAccess}");
                return hasAccess;
            }

            Logger.LogWarning($"No page role mappings available for page: {pageUrl}, mapping source: {CurrentMappingSource}");
            return false;
        }

        private bool IsPageForSelectedProduct(string pageUrl)
        {
            if (string.IsNullOrEmpty(SelectedProduct))
            {
                Logger.LogWarning("SelectedProduct is null or empty");
                return false;
            }

            bool isForProduct = ProductPageMappings.TryGetValue(SelectedProduct, out var pages) && pages.Contains(pageUrl);
            Logger.LogDebug($"Page {pageUrl} for product {SelectedProduct}: {isForProduct}");
            return isForProduct;
        }

        private string GetProductIcon(string product)
        {
            return product switch
            {
                "EHR" => Icons.Material.Filled.LocalHospital,
                "Billing" => Icons.Material.Filled.Receipt,
                _ => Icons.Material.Filled.Apps
            };
        }

        private string GetProductDisplayName(string product)
        {
            return product switch
            {
                "EHR" => Localizer["EHRProduct"],
                "Billing" => Localizer["BillingProduct"],
                _ => product
            };
        }

        private bool ShouldShowEHRSettings()
        {
            var settingsPages = new[]
            {
                Practice, Patients, Staff, LicenseActivation, Security,
                UserManagement, Templates, Config, PlanBilling
            };

            bool shouldShow = settingsPages.Any(IsPageAccessible);
            Logger.LogDebug($"ShouldShowEHRSettings: {shouldShow}");
            return shouldShow;
        }

        private void NavigateToPage(string url)
        {
            if (!string.IsNullOrEmpty(url))
            {
                NavigationManager.NavigateTo(url);
            }
        }

        private void LogNavigationState()
        {
            try
            {
                Logger.LogInformation($"Navigation State Summary:");
                Logger.LogInformation($"  - Mapping Source: {CurrentMappingSource}");
                Logger.LogInformation($"  - Custom Mappings Count: {PageRoleMappings?.Count ?? 0}");
                Logger.LogInformation($"  - Predefined Mappings Count: {PreDefinedPageRoleMappings?.Count ?? 0}");
                Logger.LogInformation($"  - Selected Product: {SelectedProduct}");
                Logger.LogInformation($"  - Has EHR Access: {HasEHRAccess}");
                Logger.LogInformation($"  - Has Billing Access: {HasBillingAccess}");
                Logger.LogInformation($"  - User Role: {activeUser?.role ?? "Unknown"}");
                Logger.LogInformation($"  - Should Show EHR Settings: {ShouldShowEHRSettings()}");

                if (CurrentMappingSource == MappingSource.CustomMappings)
                {
                    Logger.LogDebug($"  - Custom Mappings: {string.Join(", ", PageRoleMappings?.Select(p => p.PagePath) ?? new string[0])}");
                }
                else if (CurrentMappingSource == MappingSource.PreDefinedMappings)
                {
                    Logger.LogDebug($"  - Predefined Mappings: {string.Join(", ", PreDefinedPageRoleMappings?.Select(p => p.PagePath) ?? new string[0])}");
                }
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error logging navigation state: {ex.Message}");
            }
        }
    }
}
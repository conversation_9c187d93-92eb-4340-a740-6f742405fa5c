﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IFlowsheetService
    {
        Task CreateFlowsheetAsync(List<Flowsheet> Tasks, Guid? OrgID, bool Subscription);
        Task<List<Flowsheet>> GetFlowsheetAsync(Guid id, Guid? OrgID, bool Subscription);
        Task UpdateFlowsheetList(List<Flowsheet> flowsheet, Guid? OrgID, bool Subscription);
        Task<List<Flowsheet>> GetFlowsheetByIdAsyncAndIsActive(Guid id, Guid? OrgID, bool Subscription);
        Task DeleteFlowsheetAsync(Guid taskId, Guid? OrgID, bool Subscription);
        Task UpdateFlowsheetAsync(Flowsheet flowsheet, Guid? OrgID, bool Subscription);
    }
}

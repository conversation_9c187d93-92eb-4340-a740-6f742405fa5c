﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class PaymentsData:IModel
    {
        public Guid PaymentId { get; set; } 

        
        public string PaymentNo { get; set; }

        
        public string PostedBy { get; set; }

       
        public DateTime PostedDate { get; set; }

        
        public string PaymentFrom { get; set; }

        
        public string? CheckNo { get; set; } 

        
        public DateTime? CheckDate { get; set; } 

        
        public decimal Amount { get; set; }

        
        public decimal Posted { get; set; }

        public Guid? OrganizationID { get; set; }

        public bool? Subscription { get; set; }
    }
}

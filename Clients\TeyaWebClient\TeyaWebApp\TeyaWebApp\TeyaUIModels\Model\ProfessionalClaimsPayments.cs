﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class ProfessionalClaimsPayments
    {
        public Guid PaymentsId { get; set; }
        public int Id { get; set; }
        public DateTime? Date { get; set; }
        public string? From { get; set; }
        public decimal Allowed { get; set; }
        public decimal Deduct { get; set; }
        public decimal CoIns { get; set; }
        public decimal Copay { get; set; }
        public decimal Paid { get; set; }
        public decimal Adjust { get; set; }
        public decimal Withheld { get; set; }
        public Guid ProfessionalClaimsId { get; set; }
    }
}

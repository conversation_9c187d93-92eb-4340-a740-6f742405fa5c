﻿using System;
using System.ComponentModel.DataAnnotations;

namespace TeyaUIModels.Model
{
    public class ProductFeature : IModel
    {
        [Key]
        public Guid Id { get; set; }
        public string FeatureName { get; set; }
        public Guid ProdId { get; set; }
        public Guid? OrganizationID { get; set; }
        public string ProdName { get; set; }
        public bool Status { get; set; } = true;
        public DateTime Created { get; set; } = DateTime.Now;
        public DateTime? Updated { get; set; }
    }
}

﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using TeyaUIModels.Model;
using TeyaUIViewModels.TeyaUIViewModelResources;

namespace TeyaUIViewModels.ViewModel
{
    public class ProfessionalClaimsService : IProfessionalClaimsService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _Billing;
        private readonly ITokenService _tokenService;

        public ProfessionalClaimsService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _Billing = Environment.GetEnvironmentVariable("BillingURL");
            _tokenService = tokenService;
        }

        /// <summary>
        /// Get all ProfessionalClaims, both active and inactive
        /// </summary>
        public async Task<List<CompleteProfessionalClaims>> GetProfessionalClaimsByIdAsync(Guid id, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_Billing}/api/ProfessionalClaims/{id}/{OrgID}/{Subscription}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = _tokenService.AccessToken;
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<CompleteProfessionalClaims>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["AddressRetrievalFailure"]);
            }
        }

        /// <summary>
        /// Add New list of ProfessionalClaims
        /// </summary>
        public async Task AddProfessionalClaimsAsync(List<CompleteProfessionalClaims> completeProfessionalClaims, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_Billing}/api/ProfessionalClaims/Add/{OrgID}/{Subscription}";
            var accessToken = _tokenService.AccessToken;
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(completeProfessionalClaims);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            var response = await _httpClient.SendAsync(requestMessage);
            {
                if (response.IsSuccessStatusCode)
                {
                    var responseBody = await response.Content.ReadAsStringAsync();
                }
                else
                {
                    var error = await response.Content.ReadAsStringAsync();

                    throw new HttpRequestException(_localizer["TasksRetrievalFailure"]);
                }
            }
        }

        /// <summary>
        /// Delete ProfessionalClaim 
        /// </summary>
        public async Task DeleteProfessionalClaimsAsync(CompleteProfessionalClaims ProfessionalClaims, Guid id, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_Billing}/api/ProfessionalClaims/{id}/{OrgID}/{Subscription}";
            var accessToken = _tokenService.AccessToken;
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(ProfessionalClaims);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl)
            {
                Content = content
            };
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                var responseBody = await response.Content.ReadAsStringAsync();
            }
            else
            {
                var error = response.Content.ReadAsStringAsync();

                throw new HttpRequestException(_localizer["TasksRetrievalFailure"]);
            }
        }

        /// <summary>
        /// Update single ProfessionalClaim
        /// </summary>
        /// <param name = "ProfessionalClaims" ></ param >
        /// < returns ></ returns >
        public async Task UpdateProfessionalClaimsAsync(CompleteProfessionalClaims ProfessionalClaims, Guid? OrgID, bool Subscription)
        {
            var accessToken = _tokenService.AccessToken;
            var apiUrl = $"{_Billing}/api/ProfessionalClaims/{ProfessionalClaims.Id}/{OrgID}/{Subscription}";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(ProfessionalClaims);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            requestMessage.Content = content;

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }
        public async Task<List<CompleteProfessionalClaims>> GetAllProfessionalClaimsAsync(Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_Billing}/api/ProfessionalClaims/{OrgID}/{Subscription}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = _tokenService.AccessToken;
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<CompleteProfessionalClaims>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["AddressRetrievalFailure"]);
            }
        }

    }

}


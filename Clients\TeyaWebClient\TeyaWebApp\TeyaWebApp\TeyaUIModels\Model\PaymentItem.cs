﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class PaymentItem : IModel
    {
        public Guid ID { get; set; }
        public Guid InstitutionalClaimId { get; set; }
        public decimal Amount { get; set; }
        public DateTime PaymentDate { get; set; }
        public string? PaymentMethod { get; set; }
        public string? PaymentSource { get; set; }
        public string? TransactionId { get; set; }
        public string? Notes { get; set; }

        [JsonIgnore]
        public InstitutionalClaim? Claim { get; set; }
    }
}

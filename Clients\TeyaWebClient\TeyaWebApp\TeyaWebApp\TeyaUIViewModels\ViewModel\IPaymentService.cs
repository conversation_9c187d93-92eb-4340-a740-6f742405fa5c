﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TeyaUIModels.Model;
using System.Net.Http;
using Microsoft.Graph.Models;

namespace TeyaUIViewModels.ViewModel
{
    public interface IPaymentService
    {
        Task<List<PaymentsData>> GetPaymentsByPatientIdAsync(Guid id, Guid? OrgID, bool Subscription);
        Task<List<PaymentsData>> GetPaymentsByPCPIdAsync(Guid pcpId, Guid? OrgID, bool Subscription);
        Task<HttpResponseMessage> SavePaymentAsync(PaymentsData updatedPayment, Guid? OrgID, bool Subscription);
        Task<HttpResponseMessage> UploadPaymentAsync(PaymentsData newPayment, Guid? OrgID, bool Subscription);
        Task<PaymentsData> GetPaymentByIdAsync(Guid PaymentId, Guid? OrgId, bool Subscription);
        Task<List<PaymentsData>> GetAllByOrgIdAsync(Guid? OrgId, bool Subscription);
    }
}

.my-requests-page {
    min-height: 100vh;
    background-color: #f5f5f5;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 16px 0;
}

.page-title {
    display: flex;
    align-items: center;
    color: #1976d2;
    font-weight: 600;
}

.status-chips {
    display: flex;
    gap: 12px;
}

.status-chip {
    font-weight: 500;
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 64px 0;
}

.empty-state-card {
    margin-top: 32px;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 48px 24px;
    text-align: center;
}

.empty-icon {
    color: #bdbdbd;
    margin-bottom: 16px;
}

.requests-table-card {
    margin-top: 16px;
    border-radius: 12px;
    overflow: hidden;
}

.requests-table {
    background: white;
}

.patient-info {
    display: flex;
    align-items: center;
}

.patient-name {
    font-weight: 500;
    color: #333;
}

.gender-chip {
    font-size: 0.75rem;
    height: 24px;
}

.provider-info {
    display: flex;
    align-items: center;
    color: #666;
}

.date-info {
    display: flex;
    flex-direction: column;
}

.time-info {
    font-size: 0.75rem;
    color: #666;
    margin-top: 2px;
}

.action-buttons {
    display: flex;
    gap: 4px;
}

.action-button {
    margin: 0 2px;
}

.view-btn {
    background-color: #2196f3;
    color: white;
}

.view-btn:hover {
    background-color: #1976d2;
}

.approved-btn {
    background-color: #4caf50;
    color: white;
    cursor: default;
}

.rejected-btn {
    background-color: #f44336;
    color: white;
    cursor: default;
}

.status-summary {
    margin-top: 24px;
}

.status-summary-card {
    border-radius: 12px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.status-summary-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.status-summary-content {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 8px;
}

.status-icon {
    flex-shrink: 0;
}

.status-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.status-count {
    font-weight: 700;
    line-height: 1;
    margin-bottom: 4px;
}

.pending-card {
    border-left: 4px solid #ff9800;
}

.pending-card .status-icon {
    color: #ff9800;
}

.pending-card .status-count {
    color: #ff9800;
}

.approved-card {
    border-left: 4px solid #4caf50;
}

.approved-card .status-icon {
    color: #4caf50;
}

.approved-card .status-count {
    color: #4caf50;
}

.rejected-card {
    border-left: 4px solid #f44336;
}

.rejected-card .status-icon {
    color: #f44336;
}

.rejected-card .status-count {
    color: #f44336;
}

.changes-card {
    border-left: 4px solid #2196f3;
}

.changes-card .status-icon {
    color: #2196f3;
}

.changes-card .status-count {
    color: #2196f3;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
    
    .status-chips {
        width: 100%;
        justify-content: space-between;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 2px;
    }
    
    .status-summary-content {
        flex-direction: column;
        text-align: center;
        gap: 8px;
    }
    
    .status-info {
        align-items: center;
    }
}

@media (max-width: 600px) {
    .requests-table {
        font-size: 0.875rem;
    }
    
    .patient-info,
    .provider-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .date-info {
        font-size: 0.8rem;
    }
}

/* Hover effects */
.requests-table tbody tr:hover {
    background-color: #f8f9fa;
    cursor: pointer;
}

/* Status-specific styling */
.status-chip.mud-chip-color-warning {
    background-color: #ff9800 !important;
    color: white !important;
}

.status-chip.mud-chip-color-success {
    background-color: #4caf50 !important;
    color: white !important;
}

.status-chip.mud-chip-color-error {
    background-color: #f44336 !important;
    color: white !important;
}

.status-chip.mud-chip-color-info {
    background-color: #2196f3 !important;
    color: white !important;
}

/* Table styling improvements */
.requests-table th {
    background-color: #f5f5f5;
    font-weight: 600;
    color: #333;
    border-bottom: 2px solid #e0e0e0;
}

.requests-table td {
    border-bottom: 1px solid #f0f0f0;
    vertical-align: middle;
}

/* Animation for count changes */
.status-count {
    transition: all 0.3s ease;
}

.status-summary-card:hover .status-count {
    transform: scale(1.1);
}

/* Loading state for action buttons */
.action-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

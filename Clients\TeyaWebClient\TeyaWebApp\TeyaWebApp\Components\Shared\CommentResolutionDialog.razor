@using MudBlazor
@using TeyaUIModels.Model
@inject ISnackbar Snackbar

<MudDialog>
    <DialogContent>
        <div class="comment-resolution-dialog">
            <div class="dialog-header">
                <MudIcon Icon="@Icons.Material.Filled.Comment" Class="header-icon" />
                <MudText Typo="Typo.h6">Resolve Comments</MudText>
            </div>
            
            <div class="comments-list">
                @if (Comments.Any())
                {
                    @foreach (var comment in Comments)
                    {
                        <MudCard Class="comment-card" Elevation="1">
                            <MudCardContent>
                                <div class="comment-header">
                                    <div class="comment-author">
                                        <MudIcon Icon="@Icons.Material.Filled.Person" Size="Size.Small" Class="mr-1" />
                                        <MudText Typo="Typo.subtitle2">@comment.AuthorName</MudText>
                                    </div>
                                    <div class="comment-date">
                                        <MudText Typo="Typo.caption" Color="Color.Secondary">
                                            @comment.Date.ToString("MM/dd/yyyy HH:mm")
                                        </MudText>
                                    </div>
                                </div>
                                
                                <div class="comment-content">
                                    <MudText Typo="Typo.body2" Class="comment-text">
                                        @comment.Text
                                    </MudText>
                                </div>
                                
                                <div class="comment-actions">
                                    @if (comment.Status == "Pending")
                                    {
                                        <MudChip Size="Size.Small" Color="Color.Warning" Class="status-chip">
                                            Pending Resolution
                                        </MudChip>
                                        <MudButton Size="Size.Small" 
                                                   Variant="Variant.Filled" 
                                                   Color="Color.Success"
                                                   OnClick="() => ResolveComment(comment)"
                                                   Class="resolve-btn">
                                            Mark as Resolved
                                        </MudButton>
                                    }
                                    else
                                    {
                                        <MudChip Size="Size.Small" Color="Color.Success" Class="status-chip">
                                            Resolved
                                        </MudChip>
                                    }
                                </div>
                            </MudCardContent>
                        </MudCard>
                    }
                }
                else
                {
                    <div class="no-comments">
                        <MudIcon Icon="@Icons.Material.Filled.CommentBank" Size="Size.Large" Class="empty-icon" />
                        <MudText Typo="Typo.body1" Color="Color.Secondary">No comments to resolve</MudText>
                    </div>
                }
            </div>
            
            @if (HasPendingComments)
            {
                <div class="resolution-note">
                    <MudAlert Severity="Severity.Info" Class="resolution-alert">
                        <MudText Typo="Typo.body2">
                            Please resolve all pending comments before proceeding with the signature request.
                        </MudText>
                    </MudAlert>
                </div>
            }
        </div>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="Cancel" 
                   Variant="Variant.Outlined" 
                   Color="Color.Secondary"
                   Class="cancel-btn">
            Cancel
        </MudButton>
        <MudButton OnClick="ResolveAll" 
                   Variant="Variant.Filled" 
                   Color="Color.Success"
                   Disabled="!HasPendingComments"
                   Class="resolve-all-btn">
            Resolve All
        </MudButton>
        <MudButton OnClick="Complete" 
                   Variant="Variant.Filled" 
                   Color="Color.Primary"
                   Disabled="HasPendingComments"
                   Class="complete-btn">
            Complete
        </MudButton>
    </DialogActions>
</MudDialog>

<style>
    .comment-resolution-dialog {
        min-width: 600px;
        max-width: 800px;
        padding: 16px 0;
    }

    .dialog-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 24px;
        color: #1976d2;
    }

    .header-icon {
        color: #1976d2;
    }

    .comments-list {
        max-height: 400px;
        overflow-y: auto;
        margin-bottom: 16px;
    }

    .comment-card {
        margin-bottom: 16px;
        border-left: 4px solid #1976d2;
    }

    .comment-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
    }

    .comment-author {
        display: flex;
        align-items: center;
        color: #1976d2;
    }

    .comment-content {
        margin-bottom: 16px;
    }

    .comment-text {
        line-height: 1.6;
        padding: 8px 0;
    }

    .comment-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .status-chip {
        font-size: 0.75rem;
        height: 24px;
    }

    .resolve-btn {
        min-width: 120px;
        height: 32px;
    }

    .no-comments {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 48px 24px;
        text-align: center;
    }

    .empty-icon {
        color: #bdbdbd;
        margin-bottom: 16px;
    }

    .resolution-note {
        margin-top: 16px;
    }

    .resolution-alert {
        border-radius: 8px;
    }

    .cancel-btn {
        min-width: 80px;
    }

    .resolve-all-btn {
        min-width: 100px;
    }

    .complete-btn {
        min-width: 100px;
    }

    @media (max-width: 768px) {
        .comment-resolution-dialog {
            min-width: 400px;
        }
        
        .comment-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 4px;
        }
        
        .comment-actions {
            flex-direction: column;
            gap: 8px;
            align-items: stretch;
        }
        
        .resolve-btn {
            width: 100%;
        }
    }
</style>

@code {
    [CascadingParameter] MudDialogInstance MudDialog { get; set; }
    [Parameter] public List<CommentItem> Comments { get; set; } = new();
    [Parameter] public EventCallback<List<CommentItem>> OnCommentsResolved { get; set; }

    private bool HasPendingComments => Comments.Any(c => c.Status == "Pending");

    public class CommentItem
    {
        public Guid Id { get; set; } = Guid.NewGuid();
        public string AuthorName { get; set; } = string.Empty;
        public DateTime Date { get; set; } = DateTime.Now;
        public string Text { get; set; } = string.Empty;
        public string Status { get; set; } = "Pending"; // Pending, Resolved
        public string SectionName { get; set; } = string.Empty;
    }

    private void Cancel()
    {
        MudDialog.Cancel();
    }

    private async Task ResolveComment(CommentItem comment)
    {
        try
        {
            comment.Status = "Resolved";
            await OnCommentsResolved.InvokeAsync(Comments);
            Snackbar.Add("Comment resolved successfully", Severity.Success);
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Snackbar.Add("Error resolving comment", Severity.Error);
        }
    }

    private async Task ResolveAll()
    {
        try
        {
            foreach (var comment in Comments.Where(c => c.Status == "Pending"))
            {
                comment.Status = "Resolved";
            }
            
            await OnCommentsResolved.InvokeAsync(Comments);
            Snackbar.Add("All comments resolved successfully", Severity.Success);
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Snackbar.Add("Error resolving comments", Severity.Error);
        }
    }

    private async Task Complete()
    {
        if (HasPendingComments)
        {
            Snackbar.Add("Please resolve all pending comments first", Severity.Warning);
            return;
        }

        try
        {
            await OnCommentsResolved.InvokeAsync(Comments);
            MudDialog.Close(DialogResult.Ok(Comments));
        }
        catch (Exception ex)
        {
            Snackbar.Add("Error completing comment resolution", Severity.Error);
        }
    }
}

/* Signature System Global Styles */

:root {
    --signature-primary: #1976d2;
    --signature-success: #4caf50;
    --signature-warning: #ff9800;
    --signature-error: #f44336;
    --signature-info: #2196f3;
    --signature-secondary: #6c757d;
    
    --signature-bg-light: #f8f9fa;
    --signature-bg-white: #ffffff;
    --signature-border: #e0e0e0;
    --signature-border-light: #f0f0f0;
    
    --signature-shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --signature-shadow-md: 0 4px 8px rgba(0, 0, 0, 0.15);
    --signature-shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.2);
    
    --signature-radius-sm: 4px;
    --signature-radius-md: 8px;
    --signature-radius-lg: 12px;
    
    --signature-transition: all 0.2s ease;
}

/* Common signature system components */
.signature-system {
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Status indicators */
.status-pending {
    color: var(--signature-warning);
    background-color: rgba(255, 152, 0, 0.1);
}

.status-approved {
    color: var(--signature-success);
    background-color: rgba(76, 175, 80, 0.1);
}

.status-rejected {
    color: var(--signature-error);
    background-color: rgba(244, 67, 54, 0.1);
}

.status-changes-requested {
    color: var(--signature-info);
    background-color: rgba(33, 150, 243, 0.1);
}

/* Button styles */
.signature-btn {
    border-radius: var(--signature-radius-md);
    transition: var(--signature-transition);
    font-weight: 500;
    text-transform: none;
}

.signature-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--signature-shadow-md);
}

.signature-btn:active {
    transform: translateY(0);
}

.signature-btn-primary {
    background-color: var(--signature-primary);
    color: white;
}

.signature-btn-success {
    background-color: var(--signature-success);
    color: white;
}

.signature-btn-warning {
    background-color: var(--signature-warning);
    color: white;
}

.signature-btn-error {
    background-color: var(--signature-error);
    color: white;
}

/* Card styles */
.signature-card {
    border-radius: var(--signature-radius-lg);
    box-shadow: var(--signature-shadow-sm);
    border: 1px solid var(--signature-border);
    background: var(--signature-bg-white);
    transition: var(--signature-transition);
}

.signature-card:hover {
    box-shadow: var(--signature-shadow-md);
}

/* Form elements */
.signature-input {
    border-radius: var(--signature-radius-md);
    border: 1px solid var(--signature-border);
    transition: var(--signature-transition);
}

.signature-input:focus {
    border-color: var(--signature-primary);
    box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

/* Loading states */
.signature-loading {
    opacity: 0.7;
    pointer-events: none;
}

.signature-spinner {
    border: 2px solid var(--signature-border);
    border-top: 2px solid var(--signature-primary);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: signature-spin 1s linear infinite;
}

@keyframes signature-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive utilities */
@media (max-width: 768px) {
    .signature-mobile-stack {
        flex-direction: column !important;
        gap: 8px !important;
    }
    
    .signature-mobile-full {
        width: 100% !important;
    }
    
    .signature-mobile-hide {
        display: none !important;
    }
}

@media (max-width: 600px) {
    .signature-sm-hide {
        display: none !important;
    }
    
    .signature-sm-stack {
        flex-direction: column !important;
    }
}

/* Accessibility improvements */
.signature-focus:focus {
    outline: 2px solid var(--signature-primary);
    outline-offset: 2px;
}

.signature-sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Animation utilities */
.signature-fade-in {
    animation: signature-fadeIn 0.3s ease-in;
}

.signature-slide-up {
    animation: signature-slideUp 0.3s ease-out;
}

.signature-bounce {
    animation: signature-bounce 0.6s ease-out;
}

@keyframes signature-fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes signature-slideUp {
    from { 
        opacity: 0;
        transform: translateY(20px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes signature-bounce {
    0%, 20%, 53%, 80%, 100% {
        animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
        transform: translate3d(0,0,0);
    }
    40%, 43% {
        animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
        transform: translate3d(0, -30px, 0);
    }
    70% {
        animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0,-4px,0);
    }
}

/* Notification styles */
.signature-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 400px;
    border-radius: var(--signature-radius-md);
    box-shadow: var(--signature-shadow-lg);
    animation: signature-slideUp 0.3s ease-out;
}

.signature-notification-success {
    background-color: var(--signature-success);
    color: white;
}

.signature-notification-error {
    background-color: var(--signature-error);
    color: white;
}

.signature-notification-warning {
    background-color: var(--signature-warning);
    color: white;
}

.signature-notification-info {
    background-color: var(--signature-info);
    color: white;
}

/* Table enhancements */
.signature-table {
    border-collapse: collapse;
    width: 100%;
    background: var(--signature-bg-white);
    border-radius: var(--signature-radius-md);
    overflow: hidden;
    box-shadow: var(--signature-shadow-sm);
}

.signature-table th {
    background-color: var(--signature-bg-light);
    font-weight: 600;
    padding: 12px 16px;
    text-align: left;
    border-bottom: 2px solid var(--signature-border);
}

.signature-table td {
    padding: 12px 16px;
    border-bottom: 1px solid var(--signature-border-light);
}

.signature-table tr:hover {
    background-color: rgba(25, 118, 210, 0.04);
}

/* Utility classes */
.signature-text-primary { color: var(--signature-primary); }
.signature-text-success { color: var(--signature-success); }
.signature-text-warning { color: var(--signature-warning); }
.signature-text-error { color: var(--signature-error); }
.signature-text-secondary { color: var(--signature-secondary); }

.signature-bg-primary { background-color: var(--signature-primary); }
.signature-bg-success { background-color: var(--signature-success); }
.signature-bg-warning { background-color: var(--signature-warning); }
.signature-bg-error { background-color: var(--signature-error); }
.signature-bg-light { background-color: var(--signature-bg-light); }

.signature-border-primary { border-color: var(--signature-primary); }
.signature-border-success { border-color: var(--signature-success); }
.signature-border-warning { border-color: var(--signature-warning); }
.signature-border-error { border-color: var(--signature-error); }

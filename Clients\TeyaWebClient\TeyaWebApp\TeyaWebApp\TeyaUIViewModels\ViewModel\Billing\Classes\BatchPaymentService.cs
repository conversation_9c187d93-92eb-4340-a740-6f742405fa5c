﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Graph.Models;
using TeyaUIModels.Model.Billing;
using TeyaUIViewModels.TeyaUIViewModelResources;
using TeyaUIViewModels.ViewModel.Billing.Interfaces;

namespace TeyaUIViewModels.ViewModel.Billing.Classes
{
    public class BatchPaymentService : IBatchBatchPaymentservice
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<TeyaUIViewModelsStrings> _localizer;
        private readonly string _EncounterNotes;
        private readonly ITokenService _tokenService;

        public BatchPaymentService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<TeyaUIViewModelsStrings> localizer, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _EncounterNotes = Environment.GetEnvironmentVariable("BillingURL");
            _tokenService = tokenService;
        }


        public async Task<BatchPaymentsData> GetBatchPaymentByIdAsync(Guid BatchPaymentId, Guid? OrgId, bool Subscription)
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                var apiUrl = $"{_EncounterNotes}/api/BatchPayments/{BatchPaymentId}/{OrgId}/{Subscription}";

                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (!response.IsSuccessStatusCode)
                {
                    throw new HttpRequestException($"Error fetching BatchPayment: {response.StatusCode}");
                }

                var responseData = await response.Content.ReadAsStringAsync();

                if (string.IsNullOrWhiteSpace(responseData))
                    return null;

                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                return JsonSerializer.Deserialize<BatchPaymentsData>(responseData, options);
            }
            catch (Exception ex)
            {
                throw new Exception($"An error occurred while retrieving the BatchPayment: {ex.Message}", ex);
            }
        }
        public async Task<List<BatchPaymentsData>> GetAllByOrgIdAsync(Guid? OrgId, bool Subscription)
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                var apiUrl = $"{_EncounterNotes}/api/BatchPayments/{OrgId}/{Subscription}";

                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (!response.IsSuccessStatusCode)
                {
                    throw new HttpRequestException($"Error fetching BatchPayment: {response.StatusCode}");
                }

                var responseData = await response.Content.ReadAsStringAsync();

                if (string.IsNullOrWhiteSpace(responseData))
                    return null;

                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                return JsonSerializer.Deserialize<List<BatchPaymentsData>>(responseData, options) ?? new List<BatchPaymentsData>();
            }
            catch (Exception ex)
            {
                throw new Exception($"An error occurred while retrieving the BatchPayment: {ex.Message}", ex);
            }
        }


        public async Task<List<BatchPaymentsData>> GetBatchPaymentsByPatientIdAsync(Guid id, Guid? OrgID, bool Subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_EncounterNotes}/api/BatchPayments/PatientId/{id}/{OrgID}/{Subscription}";

                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (!response.IsSuccessStatusCode)
                {
                    throw new HttpRequestException($"Error fetching BatchPayments: {response.StatusCode}");
                }

                var responseData = await response.Content.ReadAsStringAsync();

                if (string.IsNullOrWhiteSpace(responseData) || responseData == "[]")
                    return new List<BatchPaymentsData>();

                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                return JsonSerializer.Deserialize<List<BatchPaymentsData>>(responseData, options) ?? new List<BatchPaymentsData>();
            }
            catch (Exception ex)
            {
                throw new Exception($"An error occurred while retrieving BatchPayments: {ex.Message}", ex);
            }
        }

        public async Task<List<BatchPaymentsData>> GetBatchPaymentsByPCPIdAsync(Guid pcpId, Guid? OrgID, bool Subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_EncounterNotes}/api/BatchPayments/ByPCP/{pcpId}/{OrgID}/{Subscription}";

                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (!response.IsSuccessStatusCode)
                {
                    throw new HttpRequestException($"Error fetching BatchPayments: {response.StatusCode}");
                }

                var responseData = await response.Content.ReadAsStringAsync();

                if (string.IsNullOrWhiteSpace(responseData) || responseData == "[]")
                    return new List<BatchPaymentsData>();

                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                return JsonSerializer.Deserialize<List<BatchPaymentsData>>(responseData, options) ?? new List<BatchPaymentsData>();
            }
            catch (Exception ex)
            {
                throw new Exception($"An error occurred while retrieving BatchPayments: {ex.Message}", ex);
            }
        }

        public async Task<HttpResponseMessage> SaveBatchPaymentAsync(BatchPaymentsData updatedBatchPayment, Guid? OrgID, bool Subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_EncounterNotes}/api/BatchPayments/{updatedBatchPayment.BatchId}/{OrgID}/{Subscription}";

                var bodyContent = JsonSerializer.Serialize(updatedBatchPayment, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
                });

                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
                var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                requestMessage.Content = content;

                var response = await _httpClient.SendAsync(requestMessage);

                return response;
            }
            catch (Exception ex)
            {
                throw new Exception($"An error occurred while Updating BatchPayment: {ex.Message}", ex);
                throw;
            }
        }

        public async Task<HttpResponseMessage> UploadBatchPaymentAsync(BatchPaymentsData updatedBatchPayment, Guid? OrgID, bool Subscription)
        {
            try
            {
                var accessToken = _tokenService.AccessToken;
                var apiUrl = $"{_EncounterNotes}/api/BatchPayments/{OrgID}/{Subscription}";

                var bodyContent = JsonSerializer.Serialize(new List<BatchPaymentsData> { updatedBatchPayment }, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
                });

                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                requestMessage.Content = content;

                var response = await _httpClient.SendAsync(requestMessage);
                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"Error response: {response.StatusCode}, Content: {errorContent}");
                }
                return response;
            }
            catch (Exception ex)
            {
                throw new Exception($"An error occurred while Updating BatchPayment: {ex.Message}", ex);
                throw;
            }
        }
    }
}


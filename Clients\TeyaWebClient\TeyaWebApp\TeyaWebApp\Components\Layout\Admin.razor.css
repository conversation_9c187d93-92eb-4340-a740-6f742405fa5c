/* Admin Layout Styles */

.org-name {
    font-weight: 600;
    font-size: 1.2rem;
    color: white;
    margin-left: 16px;
}

.search-container {
    display: flex;
    align-items: center;
    gap: 8px;
    max-width: 400px;
    width: 100%;
}

.search-autocomplete {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.search-icon-button {
    color: white;
}

.signature-nav-icons {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-right: 16px;
}

.signature-nav-btn {
    color: white;
    transition: all 0.2s ease;
}

.signature-nav-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: scale(1.05);
}

.signature-nav-btn:active {
    transform: scale(0.95);
}

/* Responsive Design */
@media (max-width: 768px) {
    .org-name {
        display: none;
    }
    
    .search-container {
        max-width: 200px;
    }
    
    .signature-nav-icons {
        margin-right: 8px;
        gap: 4px;
    }
}

@media (max-width: 600px) {
    .search-container {
        display: none;
    }
    
    .signature-nav-icons {
        margin-right: 4px;
    }
}

/* Theme-specific adjustments */
.mud-appbar {
    background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
}

.mud-drawer {
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

/* Animation for navigation icons */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.signature-nav-btn.has-notifications {
    animation: pulse 2s infinite;
}

/* Tooltip styling */
.mud-tooltip {
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    font-size: 0.875rem;
    border-radius: 4px;
    padding: 8px 12px;
}

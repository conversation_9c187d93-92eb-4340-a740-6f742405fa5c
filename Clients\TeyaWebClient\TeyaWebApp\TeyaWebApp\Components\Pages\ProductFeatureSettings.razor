﻿@page "/productfeaturesettings"
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize(Policy = "productfeaturesettingsAccessPolicy")]
@using Microsoft.Extensions.Localization
@using Syncfusion.Blazor.Grids
@using MudBlazor
@using TeyaWebApp.Components.Layout
@layout Admin
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@inject IDialogService DialogService

<GenericCard Heading="@Localizer["Products"]">
    <SfGrid DataSource="@ProductList"
            AllowPaging="true"
            AllowSorting="true"
            Toolbar="@(new List<string>() { $"{Localizer["Add"]}", $"{Localizer["Edit"]}", $"{Localizer["Delete"]}", $"{Localizer["Update"]}", $"{Localizer["Cancel"]}" })"
            EditSettings="@ProductGridEditSettings"
            @ref="ProductGrid">
        <GridEvents OnActionBegin="OnProductActionBegin"
                    OnActionComplete="OnProductActionComplete"
                    RowSelected="OnProductRowSelected"
                    TValue="Product" />
        <GridPageSettings PageSize="10" />
        <GridSortSettings>
            <GridSortColumns>
                <GridSortColumn Field="@nameof(Product.Name)" Direction="Syncfusion.Blazor.Grids.SortDirection.Ascending" />
            </GridSortColumns>
        </GridSortSettings>
        <GridColumns>
            <GridColumn Field="@nameof(Product.Id)" HeaderText="@Localizer["Product ID"]" IsPrimaryKey="true" Width="120" Visible="false" />
            <GridColumn Field="@nameof(Product.Name)" HeaderText="@Localizer["Name"]" Width="150" />
            <GridColumn Field="@nameof(Product.Description)" HeaderText="@Localizer["Description"]" Width="200" />
            <GridColumn Field="@nameof(Product.Byproduct)" HeaderText="@Localizer["By Product"]" Width="150" />
        </GridColumns>
    </SfGrid>
</GenericCard>

@if (SelectedProduct != null)
{
    <GenericCard Heading="@string.Format(Localizer["Features for {0}"], SelectedProduct?.Name)">
        @if (FeaturesForSelectedProduct != null)
        {
            <SfGrid DataSource="@FeaturesForSelectedProduct"
                    AllowPaging="true"
                    AllowSorting="true"
                    Toolbar="@(new List<string>() { Localizer["Add"], Localizer["Edit"], Localizer["Delete"], Localizer["Update"], Localizer["Cancel"] })"
                    EditSettings="@FeatureGridEditSettings"
                    @ref="FeatureGrid">
                <GridEvents OnActionBegin="OnFeatureActionBegin"
                            OnActionComplete="OnFeatureActionComplete" TValue="ProductFeature" />
                <GridPageSettings PageSize="10" />
                <GridSortSettings>
                    <GridSortColumns>
                        <GridSortColumn Field="@nameof(ProductFeature.Created)" Direction="Syncfusion.Blazor.Grids.SortDirection.Ascending" />
                    </GridSortColumns>
                </GridSortSettings>
                <GridColumns>
                    <GridColumn Field="@nameof(ProductFeature.Id)" HeaderText="@Localizer["ID"]" IsPrimaryKey="true" Visible="false" Width="0" />
                    <GridColumn Field="@nameof(ProductFeature.FeatureName)" HeaderText="@Localizer["Feature Name"]" Width="200" />
                    <GridColumn Field="@nameof(ProductFeature.ProdId)" HeaderText="@Localizer["Product ID"]" Width="150" Visible="false" />
                    <GridColumn Field="@nameof(ProductFeature.ProdName)" HeaderText="@Localizer["Product Name"]" Width="150" Visible="false" />
                    <GridColumn Field="@nameof(ProductFeature.Status)" HeaderText="@Localizer["Status"]" Width="100" Type="ColumnType.Boolean" />
                    <GridColumn Field="@nameof(ProductFeature.Created)" HeaderText="@Localizer["Created"]" Format="d" Width="130" AllowEditing="false" />
                    <GridColumn Field="@nameof(ProductFeature.Updated)" HeaderText="@Localizer["Updated"]" Format="d" Width="130" AllowEditing="false" />
                </GridColumns>
            </SfGrid>
        }
        else
        {
            <MudText Typo="Typo.body1" Color="Color.Secondary">
                @Localizer["No features available for the selected product."]
            </MudText>
        }
    </GenericCard>
}
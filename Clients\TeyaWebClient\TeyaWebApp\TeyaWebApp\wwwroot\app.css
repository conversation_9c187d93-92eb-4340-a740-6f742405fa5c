html, body {
    font-family: 'Atlassian Text', 'Charlie Text', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-size: 14px;
    line-height: 1.6;
    margin: 0;
    padding: 0;
    color: #172b4d;
    background-color: #f4f5f7;
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: #172b4d;
}

h1 {
    font-size: 1.75rem;
    color: #091e42;
}

h2 {
    font-size: 1.5rem;
}

h3 {
    font-size: 1.25rem;
}

.mud-typography {
    color: #172b4d;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5;
}

.mud-typography-h6 {
    font-size: 16px;
    font-weight: 600;
}

.mud-popover {
    max-height: 300px !important;
    overflow-y: auto !important;
}
.mud-popover {
    max-height: none !important;
    overflow: visible !important;
}

    .mud-popover .mud-list {
        max-height: 300px !important;
        overflow-y: auto !important;
    }


button, input, select, textarea {
    font-size: 14px;
    padding: 8px 12px;
    margin: 4px 0;
    border-radius: 4px;
    border: 1px solid #dfe1e6;
    background-color: white;
    font-family: inherit;
}

a, .btn-link {
    color: #0052cc;
    text-decoration: none;
    font-weight: 500;
}

    a:hover, .btn-link:hover {
        color: #0747a6;
        text-decoration: underline;
    }

.btn {
    font-family: 'Atlassian Text', Arial, sans-serif;
    font-size: 14px;
    font-weight: 500;
    border-radius: 3px;
    padding: 10px 16px;
    background-color: #0052cc;
    color: #fff;
    border: none;
    cursor: pointer;
    text-align: center;
}

    .btn:hover {
        background-color: #0747a6;
    }

    .btn:focus, .btn:active:focus {
        box-shadow: 0 0 0 2px rgba(38, 132, 255, 0.5);
    }

input, textarea, select {
    font-size: 14px;
    padding: 10px;
    border: 1px solid #dfe1e6;
    border-radius: 4px;
    width: 100%;
    box-sizing: border-box;
}

    input:focus, textarea:focus, select:focus {
        border-color: #0052cc;
        outline: none;
        box-shadow: 0 0 3px #0052cc;
    }

.content {
    padding: 1rem 2rem;
    margin: 0 auto;
    max-width: 1280px;
}

/* MudBlazor Specific Additions */

.mud-grid + .mud-grid {
    margin-top: 1rem;
}

.mud-input {
    border-radius: 4px;
    box-shadow: none;
}

.mud-input-input {
    padding: 10px !important;
    border: 1px solid #dfe1e6;
    border-radius: 4px;
    background-color: white;
}

    .mud-input-input:focus {
        border-color: #0052cc !important;
        box-shadow: 0 0 0 2px rgba(38, 132, 255, 0.2);
    }

.mud-button {
    border-radius: 4px;
    font-weight: 500;
    font-size: 14px;
    padding: 8px 16px;
}

.mud-button-filled {
    background-color: #0052cc;
    color: white;
}

    .mud-button-filled:hover {
        background-color: #0747a6;
    }

.mud-paper {
    background-color: white;
    border-radius: 6px;
    padding: 16px;
    box-shadow: 0 1px 3px rgba(9, 30, 66, 0.1);
}

.mud-tabs {
    margin-bottom: 1rem;
}

.mud-tab {
    font-weight: 500;
}

.mud-tab-active {
    color: #0052cc !important;
    border-bottom: 2px solid #0052cc !important;
}

.mud-select {
    width: 100%;
}

.mud-divider {
    margin: 1rem 0;
}

/* Navigation Styles */

.mud-navlink-style {
    font-family: 'Atlassian Text', 'Roboto', 'Avenir', 'Noto Sans', sans-serif;
    font-size: 14px;
    font-weight: 500;
    color: #172b4d;
    text-decoration: none;
    line-height: 1.5;
}

    .mud-navlink-style:hover {
        color: #0747a6;
    }



.mud-navmenu-title {
    font-size: 16px;
    font-weight: 700;
    color: #091e42;
}

/* Responsive Enhancements */

@media (max-width: 768px) {
    .content {
        padding: 1rem;
    }

    .btn {
        font-size: 12px;
    }

    .mud-navmenu-title {
        font-size: 14px;
    }

    .mud-grid + .mud-grid {
        margin-top: 0.75rem;
    }
}
.mud-navlink-style {
    padding-left: 16px !important;
}

.mud-nav-mini .mud-navlink-style {
    padding-left: 0 !important;
    justify-content: center !important;
}

/* Place this at the end of your CSS file */
 .mud-navgroup-style {
    font-size: 14px !important;
    font-weight: 600 !important;
    color: #344563 !important;
}


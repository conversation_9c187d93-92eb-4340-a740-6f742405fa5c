.document-signature-container {
    margin: 16px 0;
    width: 100%;
}

.signature-card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #ffffff;
}

.signature-header {
    display: flex;
    align-items: center;
    gap: 8px;
}

.signature-icon {
    color: #1976d2;
}

.signed-chip {
    margin-left: auto;
    background-color: #4caf50 !important;
    color: white !important;
    font-weight: 600;
    font-size: 0.75rem;
}

.signing-section {
    padding: 16px 0;
    text-align: center;
}

.sign-button {
    min-width: 140px;
    height: 40px;
    font-weight: 600;
}

.signing-options {
    margin-top: 16px;
}

.signing-panel {
    border: 1px solid #e3f2fd;
    border-radius: 8px;
    background: #fafafa;
}

.panel-title {
    display: flex;
    align-items: center;
    width: 100%;
    color: #1976d2;
    font-weight: 500;
}

.close-panel-btn {
    color: #666;
}

.signing-content {
    padding: 16px;
    background: white;
    border-radius: 0 0 8px 8px;
}

.action-radio-group {
    display: flex;
    gap: 24px;
    margin-bottom: 16px;
}

.provider-selection {
    background: #e3f2fd;
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid #1976d2;
}

.provider-select {
    margin-top: 8px;
}

.comments-section {
    background: #f5f5f5;
    padding: 16px;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.comments-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.resolve-comments-btn {
    min-width: 140px;
    height: 32px;
    font-size: 0.8rem;
}

.comment-item {
    background: white;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 12px;
    border-left: 3px solid #1976d2;
}

.comment-item:last-child {
    margin-bottom: 0;
}

.comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.comment-text {
    margin-bottom: 8px;
    line-height: 1.5;
}

.comment-status {
    background-color: #4caf50 !important;
    color: white !important;
    font-size: 0.7rem;
    height: 20px;
}

.more-comments {
    text-align: center;
    font-style: italic;
    margin-top: 8px;
    padding: 8px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 4px;
}

.action-buttons {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    padding-top: 16px;
    border-top: 1px solid #e0e0e0;
}

.cancel-btn {
    min-width: 80px;
}

.action-btn {
    min-width: 100px;
}

.signature-info {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.signature-details {
    color: #6c757d;
    line-height: 1.6;
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
}

.signature-actions {
    display: flex;
    justify-content: flex-end;
}

.sign-cosign-btn {
    color: #1976d2;
    font-size: 0.8rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .action-radio-group {
        flex-direction: column;
        gap: 12px;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 8px;
    }
    
    .cancel-btn,
    .action-btn {
        width: 100%;
    }
    
    .comment-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
}

/* Animation for loading states */
.sign-button:disabled,
.action-btn:disabled {
    opacity: 0.7;
}

/* Hover effects */
.sign-button:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(25, 118, 210, 0.3);
    transition: all 0.2s ease;
}

.action-btn:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(25, 118, 210, 0.3);
    transition: all 0.2s ease;
}

/* Focus states for accessibility */
.provider-select:focus-within {
    box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

/* Status indicators */
.status-pending {
    color: #ff9800;
}

.status-approved {
    color: #4caf50;
}

.status-rejected {
    color: #f44336;
}

.status-changes-requested {
    color: #ff5722;
}

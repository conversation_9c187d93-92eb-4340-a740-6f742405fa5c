﻿using System.Security.Claims;
using System.Text.Json;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Localization;
using MudBlazor;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Components.Pages;
using TeyaWebApp.Services;
using TeyaWebApp.ViewModel;
using Blazored.SessionStorage;

namespace TeyaWebApp.Components.Layout
{
    public partial class ProfileDialog
    {


        [Inject]
        private GraphApiService GraphApiService { get; set; }

        [Inject] private ActiveUser user { get; set; }

        [Inject] private IMemberService memberservice { get; set; }
        [Inject] private UserContext usercontext { get; set; }
        
        private string user_username { get; set; }
        private string user_email { get; set; }


        protected override async Task OnInitializedAsync()
        {
            var member = await memberservice.GetMemberByIdAsync(Guid.Parse(user.id), usercontext.ActiveUserOrganizationID, usercontext.ActiveUserSubscription);
            setUser(member.UserName, member.Email);
        }

        private void setUser(string username,string email)
        {
            user_email = email;
            user_username = username;
        }

        private string GetInitials(string name)
         {
            if (string.IsNullOrEmpty(name))
                return Localizer["GusestUser"]; 

            return name.Substring(0, 1).ToUpper();
        }

        private void NavigateToManageProfile()
        {
            NavigationManager.NavigateTo(Localizer["manageprofile"]);
        }
        private async Task signout()
        {
            await GraphApiService.GetLoggedInUserDetailsAsync();
            TokenService.AccessToken = null;

            await AuthenticationStateProvider.GetAuthenticationStateAsync();
            NavigationManager.NavigateTo(Localizer["authentication/logout"], true);
        }
    }
}

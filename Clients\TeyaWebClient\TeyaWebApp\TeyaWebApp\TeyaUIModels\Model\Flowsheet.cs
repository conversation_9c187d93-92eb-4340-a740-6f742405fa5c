﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class Flowsheet : IModel
    {
        public Guid Id { get; set; }
        public string FlowsheetName { get; set; }
        public string? SerializedData  { get; set; }
        public Guid PatientId { get; set; }
        public bool IsEditable { get; set; }
        public Guid OrganizationId { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid? UpdatedBy { get; set; }
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime? UpdatedDate { get; set; }
        public bool Subscription { get; set; }
        public bool IsActive { get; set; } = true;
    }
}

@page "/review-requests"
@using TeyaUIModels.Model
@using TeyaUIViewModels.ViewModel
@using TeyaWebApp.Components.Layout
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "NotesAccessPolicy")]
@layout Admin
@inject ISignatureService SignatureService
@inject IMemberService MemberService
@inject IProgressNotesService ProgressNotesService
@inject NavigationManager Navigation
@inject ISnackbar Snackbar
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@inject ActiveUser User
@inject IDialogService DialogService

<div class="review-requests-page">
    <MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="pa-4">
        <!-- Header -->
        <div class="page-header">
            <MudText Typo="Typo.h4" Class="page-title">
                <MudIcon Icon="@Icons.Material.Filled.Assignment" Class="mr-2" />
                Review Requests
            </MudText>
            <div class="status-chips">
                <MudChip Color="Color.Warning" Size="Size.Medium" Class="status-chip">
                    <MudIcon Icon="@Icons.Material.Filled.Schedule" Size="Size.Small" Class="mr-1" />
                    Pending: @PendingCount
                </MudChip>
                <MudChip Color="Color.Info" Size="Size.Medium" Class="status-chip">
                    <MudIcon Icon="@Icons.Material.Filled.List" Size="Size.Small" Class="mr-1" />
                    Total: @TotalCount
                </MudChip>
            </div>
        </div>

        @if (IsLoading)
        {
            <div class="loading-container">
                <MudProgressCircular Color="Color.Primary" Indeterminate="true" Size="Size.Large" />
                <MudText Typo="Typo.body1" Class="mt-2">Loading review requests...</MudText>
            </div>
        }
        else if (!ReviewRequests.Any())
        {
            <MudCard Class="empty-state-card" Elevation="2">
                <MudCardContent>
                    <div class="empty-state">
                        <MudIcon Icon="@Icons.Material.Filled.Assignment" Size="Size.Large" Class="empty-icon" />
                        <MudText Typo="Typo.h6" Class="mt-2">No Review Requests</MudText>
                        <MudText Typo="Typo.body2" Color="Color.Secondary">
                            You don't have any pending review requests at the moment.
                        </MudText>
                    </div>
                </MudCardContent>
            </MudCard>
        }
        else
        {
            @if (SelectedRequest == null)
            {
                <!-- Requests Table -->
                <MudCard Class="requests-table-card" Elevation="2">
                    <MudCardContent Class="pa-0">
                        <MudTable Items="@ReviewRequests" 
                                  Hover="true" 
                                  Striped="true" 
                                  Dense="true"
                                  Class="requests-table">
                            <HeaderContent>
                                <MudTh>Patient</MudTh>
                                <MudTh>Age</MudTh>
                                <MudTh>Gender</MudTh>
                                <MudTh>Requester</MudTh>
                                <MudTh>Status</MudTh>
                                <MudTh>Request Date</MudTh>
                                <MudTh>Actions</MudTh>
                            </HeaderContent>
                            <RowTemplate>
                                <MudTd DataLabel="Patient">
                                    <div class="patient-info">
                                        <MudIcon Icon="@Icons.Material.Filled.Person" Size="Size.Small" Class="mr-2" />
                                        <span class="patient-name">@context.PatientName</span>
                                    </div>
                                </MudTd>
                                <MudTd DataLabel="Age">@context.PatientAge</MudTd>
                                <MudTd DataLabel="Gender">
                                    <MudChip Size="Size.Small" 
                                             Color="@(context.PatientGender?.ToLower() == "female" ? Color.Secondary : Color.Primary)"
                                             Class="gender-chip">
                                        @context.PatientGender
                                    </MudChip>
                                </MudTd>
                                <MudTd DataLabel="Requester">
                                    <div class="requester-info">
                                        <MudIcon Icon="@Icons.Material.Filled.LocalHospital" Size="Size.Small" Class="mr-1" />
                                        <span>@context.RequesterName</span>
                                    </div>
                                </MudTd>
                                <MudTd DataLabel="Status">
                                    <MudChip Size="Size.Small" 
                                             Color="@GetStatusColor(context.Status)"
                                             Class="status-chip">
                                        @GetStatusText(context.Status)
                                    </MudChip>
                                </MudTd>
                                <MudTd DataLabel="Request Date">
                                    <div class="date-info">
                                        <div>@context.RequestDate.ToString("MM/dd/yyyy")</div>
                                        <div class="time-info">@context.RequestDate.ToString("HH:mm")</div>
                                    </div>
                                </MudTd>
                                <MudTd DataLabel="Actions">
                                    @if (context.Status == SignatureRequestStatus.Pending)
                                    {
                                        <MudIconButton Icon="@Icons.Material.Filled.Visibility"
                                                       Color="Color.Success"
                                                       Size="Size.Small"
                                                       OnClick="() => SelectRequest(context)"
                                                       Title="Review Request"
                                                       Class="action-button review-btn" />
                                    }
                                    else
                                    {
                                        <MudIconButton Icon="@Icons.Material.Filled.Visibility"
                                                       Color="Color.Default"
                                                       Size="Size.Small"
                                                       OnClick="() => SelectRequest(context)"
                                                       Title="View Request"
                                                       Class="action-button view-btn" />
                                    }
                                </MudTd>
                            </RowTemplate>
                        </MudTable>
                    </MudCardContent>
                </MudCard>
            }
            else
            {
                <!-- Review Interface -->
                <div class="review-interface">
                    <MudCard Class="review-card" Elevation="3">
                        <MudCardHeader>
                            <CardHeaderContent>
                                <div class="review-header">
                                    <MudText Typo="Typo.h5">Reviewing Notes - @SelectedRequest.PatientName</MudText>
                                    <div class="review-actions">
                                        <MudButton Variant="Variant.Outlined"
                                                   Color="Color.Secondary"
                                                   StartIcon="@Icons.Material.Filled.ArrowBack"
                                                   OnClick="CancelReview"
                                                   Class="cancel-review-btn">
                                            Cancel
                                        </MudButton>
                                        <MudButton Variant="Variant.Outlined"
                                                   Color="Color.Warning"
                                                   StartIcon="@Icons.Material.Filled.Edit"
                                                   OnClick="RequestChanges"
                                                   Disabled="IsProcessing"
                                                   Class="changes-btn">
                                            Request Changes
                                        </MudButton>
                                        <MudButton Variant="Variant.Filled"
                                                   Color="Color.Success"
                                                   StartIcon="@Icons.Material.Filled.CheckCircle"
                                                   OnClick="ApproveRequest"
                                                   Disabled="IsProcessing"
                                                   Class="approve-btn">
                                            @if (IsProcessing)
                                            {
                                                <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                                            }
                                            else
                                            {
                                                <span>Approve</span>
                                            }
                                        </MudButton>
                                    </div>
                                </div>
                            </CardHeaderContent>
                        </MudCardHeader>
                        <MudCardContent>
                            <!-- Patient Info -->
                            <div class="patient-details">
                                <MudText Typo="Typo.subtitle1" Class="mb-2">Patient Information</MudText>
                                <div class="patient-info-grid">
                                    <div class="info-item">
                                        <MudText Typo="Typo.caption">Patient:</MudText>
                                        <MudText Typo="Typo.body2">@SelectedRequest.PatientName</MudText>
                                    </div>
                                    <div class="info-item">
                                        <MudText Typo="Typo.caption">Requester:</MudText>
                                        <MudText Typo="Typo.body2">@SelectedRequest.RequesterName</MudText>
                                    </div>
                                    <div class="info-item">
                                        <MudText Typo="Typo.caption">Request Date:</MudText>
                                        <MudText Typo="Typo.body2">@SelectedRequest.RequestDate.ToString("MM/dd/yyyy HH:mm")</MudText>
                                    </div>
                                </div>
                            </div>

                            <!-- Notes Content -->
                            @if (SelectedRecord != null)
                            {
                                <div class="notes-content mt-4">
                                    <MudText Typo="Typo.subtitle1" Class="mb-3">Notes Content</MudText>
                                    <div class="notes-sections">
                                        @foreach (var section in NotesData)
                                        {
                                            @foreach (var kvp in section)
                                            {
                                                <MudCard Class="notes-section-card mb-3" Elevation="1">
                                                    <MudCardHeader Class="section-header">
                                                        <CardHeaderContent>
                                                            <MudText Typo="Typo.h6" Class="section-title">@kvp.Key</MudText>
                                                            <MudIconButton Icon="@Icons.Material.Filled.Comment"
                                                                           Color="Color.Primary"
                                                                           Size="Size.Small"
                                                                           OnClick="() => OpenCommentDialog(kvp.Key)"
                                                                           Title="Add Comment"
                                                                           Class="comment-btn" />
                                                        </CardHeaderContent>
                                                    </MudCardHeader>
                                                    <MudCardContent>
                                                        @foreach (var data in kvp.Value)
                                                        {
                                                            <div class="subsection mb-3">
                                                                <MudText Typo="Typo.subtitle2" Class="subsection-title">@data.Key</MudText>
                                                                <div class="subsection-content">
                                                                    @if (!string.IsNullOrEmpty(data.Value))
                                                                    {
                                                                        <MudText Typo="Typo.body2">@((MarkupString)data.Value)</MudText>
                                                                    }
                                                                    else
                                                                    {
                                                                        <MudText Typo="Typo.body2" Color="Color.Secondary" Style="font-style: italic;">
                                                                            No content available
                                                                        </MudText>
                                                                    }
                                                                </div>
                                                            </div>
                                                        }
                                                    </MudCardContent>
                                                </MudCard>
                                            }
                                        }
                                    </div>
                                </div>
                            }
                        </MudCardContent>
                    </MudCard>
                </div>
            }
        }
    </MudContainer>
</div>

@code {
    private List<SignatureRequest> ReviewRequests { get; set; } = new();
    private SignatureRequest SelectedRequest { get; set; }
    private Record SelectedRecord { get; set; }
    private List<Dictionary<string, Dictionary<string, string>>> NotesData { get; set; } = new();
    private bool IsLoading { get; set; } = true;
    private bool IsProcessing { get; set; }
    private int PendingCount => ReviewRequests.Count(r => r.Status == SignatureRequestStatus.Pending);
    private int TotalCount => ReviewRequests.Count;
    private Guid OrgID { get; set; }
    private bool Subscription { get; set; }

    protected override async Task OnInitializedAsync()
    {
        OrgID = UserContext.ActiveUserOrganizationID;
        Subscription = UserContext.ActiveUserSubscription;
        await LoadReviewRequests();
    }

    private async Task LoadReviewRequests()
    {
        IsLoading = true;
        try
        {
            var userId = Guid.Parse(User.id);
            ReviewRequests = await SignatureService.GetIncomingRequestsAsync(userId, OrgID, Subscription);
        }
        catch (Exception ex)
        {
            Snackbar.Add("Error loading review requests", Severity.Error);
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task SelectRequest(SignatureRequest request)
    {
        SelectedRequest = request;
        await LoadRequestDetails();
    }

    private async Task LoadRequestDetails()
    {
        if (SelectedRequest == null) return;

        try
        {
            SelectedRecord = await ProgressNotesService.GetRecordByIdAsync(SelectedRequest.RecordId, OrgID, Subscription);
            
            if (SelectedRecord != null && !string.IsNullOrEmpty(SelectedRecord.Notes))
            {
                var notesDict = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, string>>>(SelectedRecord.Notes);
                NotesData = new List<Dictionary<string, Dictionary<string, string>>> { notesDict };
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add("Error loading request details", Severity.Error);
        }
    }

    private void CancelReview()
    {
        SelectedRequest = null;
        SelectedRecord = null;
        NotesData.Clear();
    }

    private async Task ApproveRequest()
    {
        if (SelectedRequest == null) return;

        IsProcessing = true;
        try
        {
            var success = await SignatureService.ApproveSignatureRequestAsync(SelectedRequest.Id, string.Empty, OrgID, Subscription);
            
            if (success)
            {
                Snackbar.Add("Request approved successfully", Severity.Success);
                await LoadReviewRequests();
                CancelReview();
            }
            else
            {
                Snackbar.Add("Error approving request", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add("Error approving request", Severity.Error);
        }
        finally
        {
            IsProcessing = false;
        }
    }

    private async Task OpenCommentDialog(string sectionName)
    {
        var parameters = new DialogParameters
        {
            ["SectionName"] = sectionName,
            ["OnCommentAdded"] = EventCallback.Factory.Create<string>(this, (comment) => HandleCommentAdded(sectionName, comment))
        };

        var options = new DialogOptions
        {
            CloseButton = true,
            MaxWidth = MaxWidth.Medium,
            FullWidth = true
        };

        var dialog = await DialogService.ShowAsync<AddCommentDialog>("Add Comment", parameters, options);
        var result = await dialog.Result;

        if (!result.Cancelled)
        {
            // Comment was added successfully
            StateHasChanged();
        }
    }

    private async Task HandleCommentAdded(string sectionName, string comment)
    {
        // Here you would typically save the comment to the backend
        // For now, we'll just show a success message
        Snackbar.Add($"Comment added to {sectionName}: {comment}", Severity.Success);
    }

    private async Task RequestChanges()
    {
        if (SelectedRequest == null) return;

        IsProcessing = true;
        try
        {
            var success = await SignatureService.RequestChangesAsync(SelectedRequest.Id, "Changes requested during review", OrgID, Subscription);

            if (success)
            {
                Snackbar.Add("Changes requested successfully", Severity.Success);
                await LoadReviewRequests();
                CancelReview();
            }
            else
            {
                Snackbar.Add("Error requesting changes", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add("Error requesting changes", Severity.Error);
        }
        finally
        {
            IsProcessing = false;
        }
    }

    private Color GetStatusColor(SignatureRequestStatus status)
    {
        return status switch
        {
            SignatureRequestStatus.Pending => Color.Warning,
            SignatureRequestStatus.Approved => Color.Success,
            SignatureRequestStatus.Rejected => Color.Error,
            SignatureRequestStatus.ChangesRequested => Color.Info,
            _ => Color.Default
        };
    }

    private string GetStatusText(SignatureRequestStatus status)
    {
        return status switch
        {
            SignatureRequestStatus.Pending => "Pending",
            SignatureRequestStatus.Approved => "Approved",
            SignatureRequestStatus.Rejected => "Rejected",
            SignatureRequestStatus.ChangesRequested => "Changes Requested",
            _ => "Unknown"
        };
    }
}

﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class ActiveMedication : IModel
    {
        public Guid MedicineId { get; set; }
        public Guid PatientId { get; set; }
        public Guid OrganizationId { get; set; }
        public Guid PCPId { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid UpdatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public string? BrandName { get; set; }
        public string? DrugDetails { get; set; }
        public string? Quantity { get; set; }
        public string? Frequency { get; set; }
        public string? Route { get; set; }
        public string? Take { get; set; }
        public string? Strength { get; set; }
        public bool isActive { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? CheifComplaint { get; set; }
        public Guid? CheifComplaintId { get; set; }
        public bool Subscription { get; set; }
    }
}
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface ISignatureService
    {
        // Document Signature Operations
        Task<DocumentSignature> CreateSignatureAsync(DocumentSignature signature, Guid orgId, bool subscription);
        Task<List<DocumentSignature>> GetSignaturesByRecordIdAsync(Guid recordId, Guid orgId, bool subscription);
        Task<DocumentSignature> GetSignatureByIdAsync(Guid signatureId, Guid orgId, bool subscription);
        Task<bool> HasRecordBeenSignedAsync(Guid recordId, Guid orgId, bool subscription);
        Task<bool> HasRecordBeenCoSignedAsync(Guid recordId, Guid orgId, bool subscription);

        // Signature Request Operations
        Task<SignatureRequest> CreateSignatureRequestAsync(SignatureRequest request, Guid orgId, bool subscription);
        Task<SignatureRequest> UpdateSignatureRequestAsync(SignatureRequest request, Guid orgId, bool subscription);
        Task<List<SignatureRequest>> GetIncomingRequestsAsync(Guid reviewerId, Guid orgId, bool subscription);
        Task<List<SignatureRequest>> GetOutgoingRequestsAsync(Guid requesterId, Guid orgId, bool subscription);
        Task<SignatureRequest> GetSignatureRequestByIdAsync(Guid requestId, Guid orgId, bool subscription);
        Task<bool> ApproveSignatureRequestAsync(Guid requestId, string reviewerComments, Guid orgId, bool subscription);
        Task<bool> RejectSignatureRequestAsync(Guid requestId, string reviewerComments, Guid orgId, bool subscription);
        Task<bool> RequestChangesAsync(Guid requestId, string reviewerComments, Guid orgId, bool subscription);

        // Validation
        Task<bool> CanLockRecordAsync(Guid recordId, Guid orgId, bool subscription);
        Task<bool> HasPendingCoSignRequestAsync(Guid recordId, Guid orgId, bool subscription);
    }
}

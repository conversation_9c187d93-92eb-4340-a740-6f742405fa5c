﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Net.Http;
using Microsoft.Graph.Models;
using TeyaUIModels.Model.Billing;

namespace TeyaUIViewModels.ViewModel.Billing.Interfaces
{
    public interface IBatchBatchPaymentservice
    {
        Task<List<BatchPaymentsData>> GetBatchPaymentsByPatientIdAsync(Guid id, Guid? OrgID, bool Subscription);
        Task<List<BatchPaymentsData>> GetBatchPaymentsByPCPIdAsync(Guid pcpId, Guid? OrgID, bool Subscription);
        Task<HttpResponseMessage> SaveBatchPaymentAsync(BatchPaymentsData updatedBatchPayment, Guid? OrgID, bool Subscription);
        Task<HttpResponseMessage> UploadBatchPaymentAsync(BatchPaymentsData newBatchPayment, Guid? OrgID, bool Subscription);
        Task<BatchPaymentsData> GetBatchPaymentByIdAsync(Guid BatchId, Guid? OrgId, bool Subscription);
        Task<List<BatchPaymentsData>> GetAllByOrgIdAsync(Guid? OrgId, bool Subscription);
    }
}

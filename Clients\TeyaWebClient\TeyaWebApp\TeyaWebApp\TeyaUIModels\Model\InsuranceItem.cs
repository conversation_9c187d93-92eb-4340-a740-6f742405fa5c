﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class InsuranceItem : IModel
    {
        public Guid ID { get; set; }
        public Guid InstitutionalClaimId { get; set; }
        public string? PolicyNumber { get; set; }
        public string? InsurancePlanName { get; set; }
        public string? InsuranceCompanyName { get; set; }
        public string? InsuranceType { get; set; }
        public decimal? CoverageAmount { get; set; }
        public string? PriorAuthorizationNumber { get; set; }
        public string? RelationshipToSubscriber { get; set; }
        public string? SubscriberName { get; set; }
        public DateTime? SubscriberBirthDate { get; set; }
        public string? SubscriberEmployer { get; set; }

        [JsonIgnore]
        public InstitutionalClaim? Claim { get; set; }
    }
}

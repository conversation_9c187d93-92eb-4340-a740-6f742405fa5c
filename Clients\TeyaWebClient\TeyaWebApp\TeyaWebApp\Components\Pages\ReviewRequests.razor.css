.review-requests-page {
    min-height: 100vh;
    background-color: #f5f5f5;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 16px 0;
}

.page-title {
    display: flex;
    align-items: center;
    color: #1976d2;
    font-weight: 600;
}

.status-chips {
    display: flex;
    gap: 12px;
}

.status-chip {
    font-weight: 500;
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 64px 0;
}

.empty-state-card {
    margin-top: 32px;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 48px 24px;
    text-align: center;
}

.empty-icon {
    color: #bdbdbd;
    margin-bottom: 16px;
}

.requests-table-card {
    margin-top: 16px;
    border-radius: 12px;
    overflow: hidden;
}

.requests-table {
    background: white;
}

.patient-info {
    display: flex;
    align-items: center;
}

.patient-name {
    font-weight: 500;
    color: #333;
}

.gender-chip {
    font-size: 0.75rem;
    height: 24px;
}

.requester-info {
    display: flex;
    align-items: center;
    color: #666;
}

.date-info {
    display: flex;
    flex-direction: column;
}

.time-info {
    font-size: 0.75rem;
    color: #666;
    margin-top: 2px;
}

.action-button {
    margin: 0 2px;
}

.review-btn {
    background-color: #4caf50;
    color: white;
}

.review-btn:hover {
    background-color: #45a049;
}

.view-btn {
    background-color: #2196f3;
    color: white;
}

.view-btn:hover {
    background-color: #1976d2;
}

.review-interface {
    margin-top: 16px;
}

.review-card {
    border-radius: 12px;
    border: 2px solid #e3f2fd;
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.review-actions {
    display: flex;
    gap: 12px;
}

.cancel-review-btn {
    min-width: 100px;
}

.approve-btn {
    min-width: 120px;
    background-color: #4caf50;
}

.approve-btn:hover {
    background-color: #45a049;
}

.patient-details {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid #1976d2;
}

.patient-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-top: 12px;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.notes-content {
    border-top: 1px solid #e0e0e0;
    padding-top: 16px;
}

.notes-sections {
    max-height: 600px;
    overflow-y: auto;
    padding-right: 8px;
}

.notes-section-card {
    border-left: 4px solid #1976d2;
}

.section-header {
    background: #f5f5f5;
    padding: 12px 16px;
}

.section-title {
    color: #1976d2;
    font-weight: 600;
}

.comment-btn {
    margin-left: auto;
}

.subsection {
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 12px;
}

.subsection:last-child {
    border-bottom: none;
}

.subsection-title {
    color: #333;
    font-weight: 500;
    margin-bottom: 8px;
}

.subsection-content {
    padding-left: 16px;
    line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
    
    .status-chips {
        width: 100%;
        justify-content: space-between;
    }
    
    .review-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
    
    .review-actions {
        width: 100%;
        justify-content: space-between;
    }
    
    .patient-info-grid {
        grid-template-columns: 1fr;
    }
    
    .cancel-review-btn,
    .approve-btn {
        flex: 1;
    }
}

/* Hover effects */
.requests-table tbody tr:hover {
    background-color: #f8f9fa;
    cursor: pointer;
}

/* Status-specific styling */
.status-chip.mud-chip-color-warning {
    background-color: #ff9800 !important;
    color: white !important;
}

.status-chip.mud-chip-color-success {
    background-color: #4caf50 !important;
    color: white !important;
}

.status-chip.mud-chip-color-error {
    background-color: #f44336 !important;
    color: white !important;
}

.status-chip.mud-chip-color-info {
    background-color: #2196f3 !important;
    color: white !important;
}

/* Animation for loading states */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.approve-btn:disabled {
    animation: pulse 1.5s infinite;
}

.changes-btn {
    min-width: 140px;
    background-color: #ff9800;
    color: white;
}

.changes-btn:hover {
    background-color: #f57c00;
}

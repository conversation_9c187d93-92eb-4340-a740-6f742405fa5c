using Syncfusion.Licensing;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.Identity.Web;
using MudBlazor.Services;
using Syncfusion.Blazor;
using TeyaWebApp.Components;
using TeyaWebApp.ViewModel;
using Microsoft.Extensions.Primitives;
using Microsoft.AspNetCore.HttpOverrides;
using Blazored.SessionStorage;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using Microsoft.IdentityModel.Tokens;
using System.Security.Claims;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Options;
using System.Globalization;
using StackExchange.Redis;
using DotNetEnv;
using Microsoft.Identity.Client;
using Microsoft.AspNetCore.DataProtection;
using TeyaUIViewModels.ViewModel;
using TeyaUIModels.ViewModel;
using BusinessLayer.Services;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.Extensions.Localization;
using TeyaWebApp.TeyaAIScribeResource;
using TeyaUIModels.Model;
using Blazored.LocalStorage;
using TeyaWebApp.Services;
using TeyaUIViewModels.ViewModels;
using Microsoft.AspNetCore.Authorization;
using TeyaWebApp.Authorization;
using System.Text.Json.Serialization;
using System.Text.Json;
using Azure.Identity;
using Azure.Security.KeyVault.Secrets;
using TeyaUIViewModels.ViewModel.Billing.Interfaces;
using TeyaUIViewModels.ViewModel.Billing.Classes;
using System.Net.Http.Headers;

var environment = Environment.GetEnvironmentVariable("ENVIRONMENT_KEY") ?? "Local";

var configBuilder = new ConfigurationBuilder();

if (environment.Equals("Local", StringComparison.OrdinalIgnoreCase))
{
    configBuilder
        .AddJsonFile("appsettings.json", optional: true)
        .AddJsonFile("appsettings.local.json", optional: true)
        .AddEnvironmentVariables();
}

var config = configBuilder.Build();

if (environment.Equals("Local", StringComparison.OrdinalIgnoreCase))
{
    var localSection = config.GetSection("EnvironmentVariables");
    foreach (var child in localSection.GetChildren())
    {
        Environment.SetEnvironmentVariable(child.Key, child.Value);
    }
}

if (environment.Equals("Development", StringComparison.OrdinalIgnoreCase) ||
    environment.Equals("Production", StringComparison.OrdinalIgnoreCase))
{
    await LoadSecretsAsync(config);
}

// LoadSecretsAsync method exactly as you wrote:
static async Task LoadSecretsAsync(IConfiguration config)
{
    var keyvault_urls = Environment.GetEnvironmentVariable("KEYVAULT__URLS");
    var tenantId = Environment.GetEnvironmentVariable("KEYVAULT__TENANTID");
    var clientId = Environment.GetEnvironmentVariable("KEYVAULT__CLIENTID");
    var clientSecret = Environment.GetEnvironmentVariable("KEYVAULT__CLIENTSECRET");

    var list_keyvault_urls = keyvault_urls.Split(',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries);

    var credential = new ClientSecretCredential(
        tenantId,
        clientId,
        clientSecret,
        new ClientSecretCredentialOptions { AdditionallyAllowedTenants = { "*" } });

    foreach (var keyvaulturl in list_keyvault_urls)
    {
        var client = new SecretClient(new Uri(keyvaulturl), credential);

        await foreach (var secretProperties in client.GetPropertiesOfSecretsAsync())
        {
            var secret = await client.GetSecretAsync(secretProperties.Name);
            Environment.SetEnvironmentVariable(secretProperties.Name, secret.Value.Value);
        }
    }
}

var builder = WebApplication.CreateBuilder(args);
builder.Services.AddLogging();
builder.Services.AddLocalization();
builder.Services.AddDataProtection();
Env.Load();
builder.Services.AddServerSideBlazor();
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents()
    .AddInteractiveWebAssemblyComponents();
builder.Services.AddScoped<HtmlRenderer>();
builder.Services.AddScoped<RoleMappingState>();
builder.Services.AddScoped<RazorComponentRenderer>();
builder.Services.AddScoped<GraphApiService>();
builder.Services.AddBlazoredSessionStorage();
builder.Services.AddHttpClient<IProductService, ProductService>();
builder.Services.AddMudServices();
builder.Services.AddScoped<ITokenService, TokenService>();
builder.Services.AddBlazoredLocalStorage();
builder.Services.AddRazorPages();
builder.Services.AddServerSideBlazor();
builder.Services.AddSyncfusionBlazor();
builder.Services.AddScoped<IReviewOfSystemService, ReviewOfSystemService>();
builder.Services.AddScoped<IPageRoleMappingService, PageRoleMappingService>();
builder.Services.AddScoped<IPreDefinedPageRoleMappingService, PreDefinedPageRoleMappingService>();
builder.Services.AddScoped<IPagePathService, PagePathService>();
builder.Services.AddScoped<IMemberService, MemberService>();
builder.Services.AddScoped<IFlowsheetService, FlowsheetService>();
builder.Services.AddScoped<IDiagnosticImagingService, DiagnosticImagingService>();
builder.Services.AddScoped<IDiagnosticImagingPageService, DiagnosticImagingPageService>();
builder.Services.AddScoped<ICountryService, countryService>();

builder.Services.AddScoped<IPaymentService, PaymentService>();

builder.Services.AddScoped<IUserLicenseService, UserLicenseService>();
builder.Services.AddScoped<IPlanTypeService, PlanTypeService>();
builder.Services.AddScoped<IGraphAdminService, GraphAdminService>();
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("AppointmentsAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/appointments")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("ChartAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/chart")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("configAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/config")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("CreatememberAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/createmember")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("ClaimsLookupAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/claimslookup")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("DentalClaimsAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/dentalclaims")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("InstitutionalclaimsAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/institutionalclaims")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("inviteuserAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/inviteuser")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("LicenseActivationAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/licenseactivation")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("licenseAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/license")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("productfeaturesettingsAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/productfeaturesettings")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("manageprofileAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/manageprofile")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("NotesAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/notes")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("VisitAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/officevisit")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("patientsAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/patients")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("PlanBillingAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/planbilling")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("PracticeAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/practice")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("ProvidersAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/providers")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("SecurityAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/security")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("sendmailAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/sendmail")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("templatesAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/templates")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("updateuserAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/updateuser")));
});
builder.Services.AddAuthorizationCore(options =>
{
    options.AddPolicy("usermanagementAccessPolicy", policy =>
        policy.Requirements.Add(new DynamicRoleRequirement("/usermanagement")));
});
builder.Services.AddScoped<IAuthorizationHandler, DynamicRoleAuthorizationHandler>();

builder.Services.AddDistributedMemoryCache();
builder.Services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromHours(24);  
    options.Cookie.Name = "TeyaWeb.Session";
    options.Cookie.HttpOnly = true;
    options.Cookie.IsEssential = true;
    options.Cookie.SecurePolicy = CookieSecurePolicy.Always;
    options.Cookie.SameSite = SameSiteMode.Lax;
});

builder.Services.AddAuthentication(options =>
{
    options.DefaultScheme = CookieAuthenticationDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = OpenIdConnectDefaults.AuthenticationScheme;
})
.AddCookie(options =>
{
    options.Cookie.Name = "TeyaWeb.Auth";
    options.ExpireTimeSpan = TimeSpan.FromHours(20); 
    options.SlidingExpiration = true;
    options.Cookie.HttpOnly = true;
    options.Cookie.SecurePolicy = CookieSecurePolicy.Always;
    options.Cookie.SameSite = SameSiteMode.Lax;
    options.LoginPath = "/authentication/login";
    options.LogoutPath = "/authentication/logout";
})
.AddOpenIdConnect(options =>
{
    options.ClientId = Environment.GetEnvironmentVariable("AzureAd--ClientId");
    options.Authority = $"{Environment.GetEnvironmentVariable("AzureAd--Authority")}/v2.0";
    options.ClientSecret = Environment.GetEnvironmentVariable("AUTH-CLIENT-SECRET");
    options.ResponseType = Environment.GetEnvironmentVariable("AUTH-RESPONSE-TYPE");
    options.SaveTokens = true; // Important: This should be true to enable token refresh
    options.CallbackPath = Environment.GetEnvironmentVariable("AUTH-CALLBACK-PATH");

    // Add offline_access scope for refresh tokens
    options.Scope.Add("offline_access");
    options.Scope.Add($"api://{Environment.GetEnvironmentVariable("AzureAd--ClientId")}/access_as_user");
    options.Scope.Add(Environment.GetEnvironmentVariable("AUTH-SCOPE-1"));
    options.Scope.Add(Environment.GetEnvironmentVariable("AUTH-SCOPE-2"));

    options.TokenValidationParameters = new TokenValidationParameters
    {
        NameClaimType = "name",
        RoleClaimType = "roles"
    };

    options.Events = new OpenIdConnectEvents
    {
        OnRedirectToIdentityProviderForSignOut = context =>
        {
            var logoutUri = $"{Environment.GetEnvironmentVariable("AzureAd--Authority")}/oauth2/v2.0/logout";
            var postLogoutUri = Environment.GetEnvironmentVariable("AUTH-POST-LOGOUT-URI");

            if (!string.IsNullOrEmpty(postLogoutUri))
            {
                logoutUri += $"?post_logout_redirect_uri={Uri.EscapeDataString(postLogoutUri)}";
            }

            context.Response.Redirect(logoutUri);
            context.HandleResponse();
            return Task.CompletedTask;
        },

        OnTokenResponseReceived = async context =>
        {
            var session = context.HttpContext.Session;

            try
            {
                // Create MSAL app with token cache
                var confidentialClientApp = ConfidentialClientApplicationBuilder
                    .Create(options.ClientId)
                    .WithClientSecret(options.ClientSecret)
                    .WithAuthority(new Uri(options.Authority))
                    .Build();

                // Store the original access token from OIDC response
                session.SetString("OriginalAccessToken", context.TokenEndpointResponse.AccessToken);

                var userAccessTokenResult = await confidentialClientApp.AcquireTokenOnBehalfOf(
                    new[] { $"api://{Environment.GetEnvironmentVariable("AzureAd--ClientId")}/access_as_user" },
                    new UserAssertion(context.TokenEndpointResponse.AccessToken)
                ).ExecuteAsync();

                var graphAccessTokenResult = await confidentialClientApp.AcquireTokenOnBehalfOf(
                    new[] { $"{Environment.GetEnvironmentVariable("GRAPH-API-BASE-URL")}/.default" },
                    new UserAssertion(context.TokenEndpointResponse.AccessToken)
                ).ExecuteAsync();

                // Store tokens in Session Storage
                session.SetString("AccessToken", userAccessTokenResult.AccessToken);
                session.SetString("AccessToken2", graphAccessTokenResult.AccessToken);

                // Store refresh token if available
                if (!string.IsNullOrEmpty(context.TokenEndpointResponse.RefreshToken))
                {
                    session.SetString("RefreshToken", context.TokenEndpointResponse.RefreshToken);
                }

                // Store user information and identifiers for token refresh
                if (context.Principal?.Identity?.IsAuthenticated == true)
                {
                    var userClaims = context.Principal.Claims.ToDictionary(c => c.Type, c => c.Value);
                    var userDetailsJson = System.Text.Json.JsonSerializer.Serialize(userClaims);
                    session.SetString("UserDetails", userDetailsJson);

                    // Store user identifier for account lookup
                    var userIdentifier = context.Principal.FindFirst("oid")?.Value ??
                                       context.Principal.FindFirst("sub")?.Value ??
                                       context.Principal.FindFirst(ClaimTypes.NameIdentifier)?.Value;

                    if (!string.IsNullOrEmpty(userIdentifier))
                    {
                        session.SetString("UserIdentifier", userIdentifier);
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the error
                Console.WriteLine($"Token acquisition error: {ex.Message}");
                throw;
            }
        },

        OnAuthenticationFailed = context =>
        {
            Console.WriteLine($"Authentication failed: {context.Exception.Message}");
            context.Response.Redirect("/authentication/login");
            context.HandleResponse();
            return Task.CompletedTask;
        }
    };
});
builder.Services.Configure<JsonSerializerOptions>(options =>
{
    options.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
    options.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
    options.Converters.Add(new ActiveUserConverter());
});
builder.Services.AddServerSideBlazor()
    .AddHubOptions(options => {
        options.ClientTimeoutInterval = TimeSpan.FromSeconds(60);
        options.HandshakeTimeout = TimeSpan.FromSeconds(30);
        options.MaximumReceiveMessageSize = 32 * 1024 * 1024; // 32MB
    });

builder.Services.AddScoped<IFDBService, FDBService>();
builder.Services.AddHttpContextAccessor();
builder.Services.AddScoped<IAllergyService, AllergyService>();
builder.Services.AddScoped<IMedicalHistoryService, MedicalHistoryService>();
builder.Services.AddScoped<ICommunicationService, CommunicationService>();
builder.Services.AddScoped<IAddressService, AddressService>();
builder.Services.AddScoped<IGuardianService, GuardianService>();
builder.Services.AddScoped<IEmployerService, EmployerService>();
builder.Services.AddScoped<IRoleService, RoleService>();
builder.Services.AddScoped<IOrganizationService, OrganizationService>();
builder.Services.AddScoped<IUserTypeService, UserTypeService>();
builder.Services.AddScoped<IHistoryOfPresentIllnessService, HistoryOfPresentIllnessService>();
builder.Services.AddScoped<ISymptomsService, SymptomsService>();
builder.Services.AddScoped<IProductService, ProductService>();
builder.Services.AddScoped<IChartService, ChartService>();
builder.Services.AddScoped<IUpToDateService, UpToDateService>();
builder.Services.AddScoped<IInsuranceService, InsuranceService>();
builder.Services.AddScoped<ActiveUser>();
builder.Services.AddScoped<UserContext>();
builder.Services.AddScoped<PatientService>();
builder.Services.AddHttpContextAccessor();
builder.Services.AddScoped<IUserThemeService, UserThemeService>();
builder.Services.AddScoped<StateContainer>();
builder.Services.AddScoped<IPageRoleMappingService, PageRoleMappingService>();
builder.Services.AddScoped<IHospitalizationRecordService, HospitalizationRecordService>();
builder.Services.AddScoped<IMemberService, MemberService>();
builder.Services.AddScoped<IICDService, ICDService>();
builder.Services.AddScoped<ITherapeuticInterventionsListService, TherapeuticInterventionsListService>();
builder.Services.AddScoped<ISoapNotesComponentsService, SoapNotesComponentsService>();
builder.Services.AddScoped<ISurgicalService, SurgicalService>();
builder.Services.AddScoped<ICustomImmunizationAlertService, CustomImmunizationAlertService>();
builder.Services.AddScoped<IInstitutionalClaimsService, InstitutionalClaimsService>();
builder.Services.AddScoped<IVaccineService, VaccineService>();
builder.Services.AddScoped<IImmunizationService, ImmunizationService>();
builder.Services.AddScoped<IPhysicalService, PhysicalService>();
builder.Services.AddScoped<IPredefinedVisitTypeService, PredefinedVisitTypeService>();
builder.Services.AddScoped<IRoleslistService, RoleslistService>();
builder.Services.AddScoped<IFacilitylistService, FacilitylistService>();
builder.Services.AddScoped<IPastResultService, PastResultService>();
builder.Services.AddScoped<IAppointmentService, AppointmentService>();
builder.Services.AddScoped<IOrganizationService, OrganizationService>();
builder.Services.AddScoped<IProductOrganizationMappingService, ProductOrganizationMappingService>();
builder.Services.AddScoped<IRoleService, RoleService>();
builder.Services.AddScoped<IFacilityService, FacilityService>();
builder.Services.AddScoped<IProductLicenseService, ProductLicenseService>();
builder.Services.AddScoped<IPracticeService, PracticeService>();
builder.Services.AddScoped<IOfficeVisitService, OfficeVisitService>();
builder.Services.AddScoped<ICacheService, RedisCacheService>();
builder.Services.AddScoped<IVisitTypeService, VisitTypeService>();
builder.Services.AddScoped<IVisitStatusService, VisitStatusService>();
builder.Services.AddScoped<ISocialHistoryService, SocialHistoryService>();
builder.Services.AddScoped<IVitalService, VitalService>();
builder.Services.AddScoped<IReferralOutgoingService, ReferralOutgoingService>();
builder.Services.AddScoped<IRxNormService, RxNormService>();
builder.Services.AddScoped<IOrderSetService, OrderSetService>();
builder.Services.AddScoped<ICurrentMedicationService, CurrentMedicationService>();
builder.Services.AddScoped<IPrescriptionMedicationService, PrescriptionMedicationService>();
builder.Services.AddScoped<IFamilyMemberService, FamilyMemberService>();
builder.Services.AddScoped<IRelationService, RelationService>();
builder.Services.AddScoped<SpeechService>();
builder.Services.AddScoped<ICPTService, CPTService>();
builder.Services.AddScoped<IMeasureService, MeasureService>();
builder.Services.AddScoped<IProductFeatureService, ProductFeatureService>();
builder.Services.AddHttpClient<ISpeechService, SpeechService>(client =>
{
    client.BaseAddress = new Uri("http://localhost/TeyaWebApi/");
});
builder.Services.AddScoped<InviteMailParametersService>();
builder.Services.AddRazorPages();
builder.Services.AddHttpContextAccessor();
builder.Services.AddSyncfusionBlazor();
builder.Services.Configure<ForwardedHeadersOptions>(options =>
{
    options.ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto;
});
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll",
        builder =>
        {
            builder.AllowAnyOrigin()
                   .AllowAnyMethod()
                   .AllowAnyHeader();
        });
});
builder.Services.AddMudServices();
builder.Configuration.AddEnvironmentVariables();
builder.Services.AddRedisCache(builder.Configuration);
builder.Services.AddSyncfusionBlazor();
builder.Services.AddScoped<ICacheService, RedisCacheService>();
Env.Load();

builder.Services.AddScoped<IChiefComplaintService, ChiefComplaintService>();
builder.Services.AddScoped<ITemplateService, TemplateService>();
builder.Services.AddScoped<IVisionExaminationService, VisionExaminationService>();
builder.Services.AddScoped<IPredefinedTemplateService, PredefinedTemplateService>();
builder.Services.AddScoped<IAssessmentsService, AssessmentsService>();
builder.Services.AddScoped<ITherapeuticInterventionsService, TherapeuticInterventionsService>();
builder.Services.AddScoped<IPhysicalTherapyService, PhysicalTherapyService>();
builder.Services.AddScoped<IProcedureService, ProcedureService>();
builder.Services.AddScoped<SharedNotesService>();
builder.Services.AddScoped<IObHistoryService, ObHistoryService>();
builder.Services.AddScoped<IGynHistoryService, GynHistoryService>();
builder.Services.AddScoped<IExaminationService, ExaminationService>();
builder.Services.AddScoped<IProgressNotesService, ProgressNotesService>();
builder.Services.AddScoped<ISignatureService, SignatureService>();
builder.Services.AddScoped<ILabTestsService, LabTestService>();
builder.Services.AddScoped<IProductFeatureService, ProductFeatureService>();
builder.Services.AddScoped<ICustomLabAlertService, CustomLabAlertService>();
builder.Services.AddScoped<IPatientSpecificAlertsService, PatientSpecificAlertsService>();
builder.Services.AddScoped<IAlertService, AlertService>();

builder.Services.AddScoped<IDentalClaimsService, DentalClaimsService>();
builder.Services.AddScoped<IERAService, ERAService>();
builder.Services.AddScoped<IProfessionalClaimsService, ProfessionalClaimsService>();

builder.Services.AddScoped<ICustomProcedureAlertService, CustomProcedureAlertService>();
builder.Services.AddScoped<IDiagnosticImagingAlertService, DiagnosticImagingAlertService>();
builder.Services.AddScoped<IDXAlertService, DXAlertService>();
builder.Services.AddScoped<IPreventiveMedicineService, PreventiveMedicineService>();

Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense(Environment.GetEnvironmentVariable("SyncfusionKey"));

var app = builder.Build();

if (app.Environment.IsDevelopment())
{
    app.UseForwardedHeaders();
    app.UseWebAssemblyDebugging();
}
else
{
    app.UseForwardedHeaders();
    app.UseHttpsRedirection();
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    app.UseHsts();
}
app.UseForwardedHeaders(new ForwardedHeadersOptions
{
    ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto
});
app.UseSession();
app.UseCors("AllowAll");
app.UseHttpsRedirection();
app.UseStaticFiles();
app.UseAntiforgery();
app.UseAuthentication();
app.UseAuthorization();
app.UseForwardedHeaders();

app.Use((context, next) =>
{
    if (context.Request.Headers.TryGetValue("X-Forwarded-PathBase", out StringValues pathBase))
    {
        context.Request.PathBase = new PathString(pathBase);
    }

    if (context.Request.Headers.TryGetValue("X-Forwarded-Proto", out StringValues proto))
    {
        context.Request.Protocol = proto;
    }

    return next();
});

app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode()
    .AddInteractiveWebAssemblyRenderMode();

app.MapGet("/authentication/logout", async context =>
{
    try
    {
        // Clear session data
        context.Session.Clear();

        // Sign out from cookie authentication
        await context.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);

        // Get the post logout redirect URI
        var postLogoutUri = Environment.GetEnvironmentVariable("AUTH-POST-LOGOUT-URI");

        // Construct the Azure AD logout URL
        var logoutUri = $"{Environment.GetEnvironmentVariable("AzureAd--Authority")}/oauth2/v2.0/logout";
        if (!string.IsNullOrEmpty(postLogoutUri))
        {
            logoutUri += $"?post_logout_redirect_uri={Uri.EscapeDataString(postLogoutUri)}";
        }

        // Clear cookies
        context.Response.Cookies.Delete("TeyaWeb.Auth");
        context.Response.Cookies.Delete("TeyaWeb.Session");
        context.Response.Cookies.Delete(".AspNetCore.Identity.Application");
        context.Response.Cookies.Delete(".AspNetCore.Antiforgery");

        // Redirect to Azure AD logout
        context.Response.Redirect(logoutUri);
    }
    catch (Exception ex)
    {
        // Log error and redirect to home
        Console.WriteLine($"Logout error: {ex.Message}");
        context.Response.Redirect("/");
    }
});
// Add this endpoint for audio uploads
// Add this before app.Run() in your Program.cs

app.MapPost("/api/upload-audio", async (HttpContext context) =>
{
    var accessToken = context.Session.GetString("AccessToken");

    var form = await context.Request.ReadFormAsync();
    var audioFile = form.Files["audioFile"];

    if (audioFile == null) return Results.BadRequest("No audio file");

    using var httpClient = new HttpClient();
    using var formData = new MultipartFormDataContent();

    var fileContent = new StreamContent(audioFile.OpenReadStream());
    formData.Add(fileContent, "audioFile", audioFile.FileName);

    httpClient.DefaultRequestHeaders.Authorization =
        new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

    var actualUploadUrl = $"{Environment.GetEnvironmentVariable("EncounterNotesURL")}/Speech/upload";
    var response = await httpClient.PostAsync(actualUploadUrl, formData);

    if (response.IsSuccessStatusCode)
    {
        var content = await response.Content.ReadAsStringAsync();
        return Results.Ok(new { message = content });
    }

    return Results.Problem("Upload failed");
});
app.MapGet("/authentication/login", async context =>
{
    await context.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
    await context.SignOutAsync(OpenIdConnectDefaults.AuthenticationScheme);
    context.Response.Cookies.Delete(".AspNetCore.Identity.Application");

    context.Response.Redirect("/");
});
app.Run();


public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddRedisCache(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddSingleton<IConnectionMultiplexer>(sp =>
        {
            var connectionString = Environment.GetEnvironmentVariable("RedisConnectionString");
            return ConnectionMultiplexer.Connect(connectionString);
        });

        services.AddSingleton<ICacheService, RedisCacheService>();

        return services;
    }
}
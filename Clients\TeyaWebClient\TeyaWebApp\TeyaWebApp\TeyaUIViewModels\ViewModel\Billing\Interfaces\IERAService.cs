﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Net.Http;
using Microsoft.Graph.Models;
using TeyaUIModels.Model.Billing;

namespace TeyaUIViewModels.ViewModel.Billing.Interfaces
{
    public interface IERAService
    {
        Task<List<CompleteERA>> GetERAByIdAsync(Guid id, Guid? OrgID, bool Subscription);
        Task<List<CompleteERA>> GetAllERAAsync(Guid? OrgID, bool Subscription);
        Task AddERAAsync(List<CompleteERA> completeERA, Guid? OrgID, bool Subscription);
        Task UpdateERAAsync(CompleteERA ERA, Guid? OrgID, bool Subscription);
        Task DeleteERAAsync(CompleteERA ERA, Guid id, Guid? OrgID, bool Subscription);


    }
}
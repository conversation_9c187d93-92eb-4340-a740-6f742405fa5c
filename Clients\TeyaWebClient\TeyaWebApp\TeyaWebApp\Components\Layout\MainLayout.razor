﻿@inherits LayoutComponentBase
@using Microsoft.AspNetCore.Components.Web
@inject IStringLocalizer<TeyaAIScribeResource> Localizer

<MudThemeProvider />
<MudPopoverProvider />
<MudDialogProvider />
<MudSnackbarProvider />

<div class="page">
    <div class="sidebar" id="sidebar">
        <NavMenu />
    </div>
    <main class="main-content" id="main-content">
        <article class="content px-4">
            <ErrorBoundary @ref="errorBoundary">
                <ChildContent>
                    @Body
                </ChildContent>
                <ErrorContent Context="exception">
                    <div class="error-container">
                        <h1>@Localizer["Something went wrong"]</h1>
                        <p>@Localizer["Sorry, an error occurred while rendering this page."]</p>
                        <div class="error-details">
                            <h3>@Localizer["Error Details:"]</h3>
                            <p>@exception.Message</p>
                            <button class="btn btn-primary" @onclick="RecoverFromError">@Localizer["Try Again"]</button>
                        </div>
                    </div>
                </ErrorContent>
            </ErrorBoundary>
        </article>
    </main>
</div>

<style>
    .page {
        position: relative;
        display: flex;
        flex-direction: row;
        height: 100vh;
        overflow: hidden;
    }

    .sidebar {
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        z-index: 1000;
        background-color: white;
        box-shadow: 2px 0 4px rgba(0,0,0,0.1);
        transition: width 0.3s ease;
        overflow-y: auto;
        overflow-x: hidden;
    }

    .main-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        height: 100vh;
        overflow-y: auto;
        transition: margin-left 0.3s ease;
        margin-left: 240px; /* Default expanded width */
    }

    /* When sidebar is collapsed */
    .sidebar.collapsed {
        width: 72px;
    }

    .main-content.expanded {
        margin-left: 240px;
    }

    .main-content.collapsed {
        margin-left: 72px;
    }

    .content {
        padding: 1rem 2rem;
        flex: 1;
        width: 100%;
        box-sizing: border-box;
    }

    .error-container {
        text-align: center;
        padding: 2rem;
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 4px;
        margin: 2rem 0;
    }

    .error-details {
        margin-top: 1rem;
        text-align: left;
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 4px;
        border: 1px solid #dee2e6;
    }

    @@media (max-width: 768px) {
        .sidebar

    {
        width: 72px !important;
    }

    .main-content {
        margin-left: 72px !important;
    }

    .content {
        padding: 1rem;
    }

    }
</style>

@code {
    private ErrorBoundary? errorBoundary;

    protected override void OnParametersSet()
    {
        errorBoundary?.Recover();
    }

    private void RecoverFromError()
    {
        errorBoundary?.Recover();
    }
}
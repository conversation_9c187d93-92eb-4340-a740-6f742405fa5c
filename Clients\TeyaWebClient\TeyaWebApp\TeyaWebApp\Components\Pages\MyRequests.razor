@page "/my-requests"
@using TeyaUIModels.Model
@using TeyaUIViewModels.ViewModel
@using TeyaWebApp.Components.Layout
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "NotesAccessPolicy")]
@layout Admin
@inject ISignatureService SignatureService
@inject NavigationManager Navigation
@inject ISnackbar Snackbar
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@inject ActiveUser User

<div class="my-requests-page">
    <MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="pa-4">
        <!-- Header -->
        <div class="page-header">
            <MudText Typo="Typo.h4" Class="page-title">
                <MudIcon Icon="@Icons.Material.Filled.Send" Class="mr-2" />
                My Requests
            </MudText>
            <div class="status-chips">
                <MudChip Color="Color.Info" Size="Size.Medium" Class="status-chip">
                    <MudIcon Icon="@Icons.Material.Filled.Send" Size="Size.Small" Class="mr-1" />
                    Outgoing Requests: @TotalCount
                </MudChip>
            </div>
        </div>

        @if (IsLoading)
        {
            <div class="loading-container">
                <MudProgressCircular Color="Color.Primary" Indeterminate="true" Size="Size.Large" />
                <MudText Typo="Typo.body1" Class="mt-2">Loading your requests...</MudText>
            </div>
        }
        else if (!MyRequests.Any())
        {
            <MudCard Class="empty-state-card" Elevation="2">
                <MudCardContent>
                    <div class="empty-state">
                        <MudIcon Icon="@Icons.Material.Filled.Send" Size="Size.Large" Class="empty-icon" />
                        <MudText Typo="Typo.h6" Class="mt-2">No Outgoing Requests</MudText>
                        <MudText Typo="Typo.body2" Color="Color.Secondary">
                            You haven't sent any cosign requests yet.
                        </MudText>
                    </div>
                </MudCardContent>
            </MudCard>
        }
        else
        {
            <!-- Requests Table -->
            <MudCard Class="requests-table-card" Elevation="2">
                <MudCardContent Class="pa-0">
                    <MudTable Items="@MyRequests" 
                              Hover="true" 
                              Striped="true" 
                              Dense="true"
                              Class="requests-table">
                        <HeaderContent>
                            <MudTh>Patient</MudTh>
                            <MudTh>Age</MudTh>
                            <MudTh>Gender</MudTh>
                            <MudTh>Requested Provider</MudTh>
                            <MudTh>Status</MudTh>
                            <MudTh>Request Date</MudTh>
                            <MudTh>Action Date</MudTh>
                            <MudTh>Actions</MudTh>
                        </HeaderContent>
                        <RowTemplate>
                            <MudTd DataLabel="Patient">
                                <div class="patient-info">
                                    <MudIcon Icon="@Icons.Material.Filled.Person" Size="Size.Small" Class="mr-2" />
                                    <span class="patient-name">@context.PatientName</span>
                                </div>
                            </MudTd>
                            <MudTd DataLabel="Age">@context.PatientAge</MudTd>
                            <MudTd DataLabel="Gender">
                                <MudChip Size="Size.Small" 
                                         Color="@(context.PatientGender?.ToLower() == "female" ? Color.Secondary : Color.Primary)"
                                         Class="gender-chip">
                                    @context.PatientGender
                                </MudChip>
                            </MudTd>
                            <MudTd DataLabel="Requested Provider">
                                <div class="provider-info">
                                    <MudIcon Icon="@Icons.Material.Filled.LocalHospital" Size="Size.Small" Class="mr-1" />
                                    <span>@context.ReviewerName</span>
                                </div>
                            </MudTd>
                            <MudTd DataLabel="Status">
                                <MudChip Size="Size.Small" 
                                         Color="@GetStatusColor(context.Status)"
                                         Class="status-chip">
                                    @GetStatusText(context.Status)
                                </MudChip>
                            </MudTd>
                            <MudTd DataLabel="Request Date">
                                <div class="date-info">
                                    <div>@context.RequestDate.ToString("MM/dd/yyyy")</div>
                                    <div class="time-info">@context.RequestDate.ToString("HH:mm")</div>
                                </div>
                            </MudTd>
                            <MudTd DataLabel="Action Date">
                                @if (context.ActionDate.HasValue)
                                {
                                    <div class="date-info">
                                        <div>@context.ActionDate.Value.ToString("MM/dd/yyyy")</div>
                                        <div class="time-info">@context.ActionDate.Value.ToString("HH:mm")</div>
                                    </div>
                                }
                                else
                                {
                                    <MudText Typo="Typo.caption" Color="Color.Secondary" Style="font-style: italic;">
                                        Pending
                                    </MudText>
                                }
                            </MudTd>
                            <MudTd DataLabel="Actions">
                                <div class="action-buttons">
                                    <MudIconButton Icon="@Icons.Material.Filled.Visibility"
                                                   Color="Color.Primary"
                                                   Size="Size.Small"
                                                   OnClick="() => ViewNotes(context)"
                                                   Title="View Notes"
                                                   Class="action-button view-btn" />
                                    
                                    @if (context.Status == SignatureRequestStatus.Approved)
                                    {
                                        <MudIconButton Icon="@Icons.Material.Filled.CheckCircle"
                                                       Color="Color.Success"
                                                       Size="Size.Small"
                                                       Title="Approved"
                                                       Class="action-button approved-btn" />
                                    }
                                    else if (context.Status == SignatureRequestStatus.Rejected)
                                    {
                                        <MudIconButton Icon="@Icons.Material.Filled.Cancel"
                                                       Color="Color.Error"
                                                       Size="Size.Small"
                                                       Title="Rejected"
                                                       Class="action-button rejected-btn" />
                                    }
                                </div>
                            </MudTd>
                        </RowTemplate>
                    </MudTable>
                </MudCardContent>
            </MudCard>

            <!-- Status Summary Cards -->
            <div class="status-summary mt-4">
                <MudGrid>
                    <MudItem xs="12" sm="6" md="3">
                        <MudCard Class="status-summary-card pending-card" Elevation="1">
                            <MudCardContent>
                                <div class="status-summary-content">
                                    <MudIcon Icon="@Icons.Material.Filled.Schedule" Size="Size.Large" Class="status-icon" />
                                    <div class="status-info">
                                        <MudText Typo="Typo.h4" Class="status-count">@PendingCount</MudText>
                                        <MudText Typo="Typo.body2">Pending</MudText>
                                    </div>
                                </div>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                    <MudItem xs="12" sm="6" md="3">
                        <MudCard Class="status-summary-card approved-card" Elevation="1">
                            <MudCardContent>
                                <div class="status-summary-content">
                                    <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Size="Size.Large" Class="status-icon" />
                                    <div class="status-info">
                                        <MudText Typo="Typo.h4" Class="status-count">@ApprovedCount</MudText>
                                        <MudText Typo="Typo.body2">Approved</MudText>
                                    </div>
                                </div>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                    <MudItem xs="12" sm="6" md="3">
                        <MudCard Class="status-summary-card rejected-card" Elevation="1">
                            <MudCardContent>
                                <div class="status-summary-content">
                                    <MudIcon Icon="@Icons.Material.Filled.Cancel" Size="Size.Large" Class="status-icon" />
                                    <div class="status-info">
                                        <MudText Typo="Typo.h4" Class="status-count">@RejectedCount</MudText>
                                        <MudText Typo="Typo.body2">Rejected</MudText>
                                    </div>
                                </div>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                    <MudItem xs="12" sm="6" md="3">
                        <MudCard Class="status-summary-card changes-card" Elevation="1">
                            <MudCardContent>
                                <div class="status-summary-content">
                                    <MudIcon Icon="@Icons.Material.Filled.Edit" Size="Size.Large" Class="status-icon" />
                                    <div class="status-info">
                                        <MudText Typo="Typo.h4" Class="status-count">@ChangesRequestedCount</MudText>
                                        <MudText Typo="Typo.body2">Changes Requested</MudText>
                                    </div>
                                </div>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                </MudGrid>
            </div>
        }
    </MudContainer>
</div>

@code {
    private List<SignatureRequest> MyRequests { get; set; } = new();
    private bool IsLoading { get; set; } = true;
    private int TotalCount => MyRequests.Count;
    private int PendingCount => MyRequests.Count(r => r.Status == SignatureRequestStatus.Pending);
    private int ApprovedCount => MyRequests.Count(r => r.Status == SignatureRequestStatus.Approved);
    private int RejectedCount => MyRequests.Count(r => r.Status == SignatureRequestStatus.Rejected);
    private int ChangesRequestedCount => MyRequests.Count(r => r.Status == SignatureRequestStatus.ChangesRequested);
    private Guid OrgID { get; set; }
    private bool Subscription { get; set; }

    protected override async Task OnInitializedAsync()
    {
        OrgID = UserContext.ActiveUserOrganizationID;
        Subscription = UserContext.ActiveUserSubscription;
        await LoadMyRequests();
    }

    private async Task LoadMyRequests()
    {
        IsLoading = true;
        try
        {
            var userId = Guid.Parse(User.id);
            MyRequests = await SignatureService.GetOutgoingRequestsAsync(userId, OrgID, Subscription);
        }
        catch (Exception ex)
        {
            Snackbar.Add("Error loading your requests", Severity.Error);
        }
        finally
        {
            IsLoading = false;
        }
    }

    private void ViewNotes(SignatureRequest request)
    {
        // Navigate to notes page with the specific record
        Navigation.NavigateTo($"/notes?recordId={request.RecordId}&patientId={request.PatientId}");
    }

    private Color GetStatusColor(SignatureRequestStatus status)
    {
        return status switch
        {
            SignatureRequestStatus.Pending => Color.Warning,
            SignatureRequestStatus.Approved => Color.Success,
            SignatureRequestStatus.Rejected => Color.Error,
            SignatureRequestStatus.ChangesRequested => Color.Info,
            _ => Color.Default
        };
    }

    private string GetStatusText(SignatureRequestStatus status)
    {
        return status switch
        {
            SignatureRequestStatus.Pending => "Pending",
            SignatureRequestStatus.Approved => "Approved",
            SignatureRequestStatus.Rejected => "Rejected",
            SignatureRequestStatus.ChangesRequested => "Changes Requested",
            _ => "Unknown"
        };
    }
}

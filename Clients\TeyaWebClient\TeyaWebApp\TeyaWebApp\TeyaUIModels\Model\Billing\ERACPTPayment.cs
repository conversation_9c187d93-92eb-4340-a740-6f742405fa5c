﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model.Billing
{
    public class ERACPTPayment
    {
        public Guid CPTPaymentId { get; set; }
        public DateTime ServiceDate { get; set; }
        public string? POS { get; set; } = string.Empty;
        public decimal Units { get; set; }
        public string? Code { get; set; } = string.Empty;
        public decimal Billed { get; set; }
        public decimal Balance { get; set; }
        public decimal Allowed { get; set; }
        public decimal Deduct { get; set; }
        public decimal Coins { get; set; }
        public decimal CoPay { get; set; }
        public decimal Paid { get; set; }
        public decimal Adjust { get; set; }
        public decimal Withhold { get; set; }
        public string? DenialCode { get; set; } = string.Empty;
        public Guid ERAId { get; set; }
    }
}

﻿@page "/security"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "SecurityAccessPolicy")]
@using TeyaUIViewModels.ViewModel
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@inject IDialogService DialogService
@layout Admin
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.ViewModel

<GenericCard Heading="@Localizer["Security Settings"]">
    <!-- Organization Selection Section -->
    <MudGrid Class="mb-4">
        <MudItem xs="12">
            <MudPaper Class="pa-4" Elevation="2">
                <div class="d-flex align-items-center mb-4" style="margin-left: -8px;">
                    <MudIcon Icon="@Icons.Material.Filled.Business" Class="mr-2" Style="color: var(--mud-palette-primary);" />
                    <MudText Typo="Typo.h6">@Localizer["SelectOrganization"]</MudText>
                </div>

                @if (Organizations != null && Organizations.Any())
                {
                    <div class="d-flex align-items-center gap-3 flex-wrap" style="margin-left: -8px;">
                        <div style="min-width: 360px; max-width: 460px;">
                            <MudAutocomplete T="Organization"
                                             @bind-Value="SelectedOrganization"
                                             SearchFunc="@SearchOrganizations"
                                             ToStringFunc="@(org => org?.OrganizationName ?? string.Empty)"
                                             Variant="Variant.Outlined"
                                             Placeholder="Seacrh Organization"
                                             Dense="true"
                                             Margin="Margin.Dense"
                                             Class="compact-search-box"
                                             Clearable="true"
                                             ResetValueOnEmptyText="true"
                                             CoerceText="false"
                                             CoerceValue="false"
                                             MaxItems="null"
                                             ShowProgressIndicator="true">
                                <ItemTemplate Context="org">
                                    <div class="d-flex align-items-center">
                                        <MudIcon Icon="@Icons.Material.Filled.Business" Size="Size.Small" Class="mr-2" Style="color: var(--mud-palette-primary);" />
                                        <MudText Typo="Typo.body2">@org.OrganizationName</MudText>
                                    </div>
                                </ItemTemplate>
                            </MudAutocomplete>
                        </div>
                        @if (SelectedOrganization != null)
                        {
                            <div class="d-flex align-items-center" style="height: 36px;">
                                <MudChip T="string"
                                         Color="Color.Success"
                                         Size="Size.Medium"
                                         Icon="@Icons.Material.Filled.CheckCircle"
                                         Style="margin: 0;">
                                    @Localizer["Selected"]: @SelectedOrganization.OrganizationName
                                </MudChip>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="text-center pa-4">
                        <MudIcon Icon="@Icons.Material.Filled.BusinessCenter" Style="font-size: 3rem; color: #cbd5e1;" />
                        <MudText Typo="Typo.body1" Class="mt-2">@Localizer["NoOrganizationsAvailable"]</MudText>
                    </div>
                }
            </MudPaper>
        </MudItem>
    </MudGrid>

    <!-- Product Management Section -->
    @if (SelectedOrganization != null && Products != null)
    {
        <MudGrid Class="mb-4">
            <MudItem xs="12">
                <MudPaper Class="pa-4" Elevation="2">
                    <div class="d-flex align-items-center mb-3">
                        <MudIcon Icon="@Icons.Material.Filled.Inventory" Class="mr-2" Style="color: var(--mud-palette-primary);" />
                        <MudText Typo="Typo.h6">
                            @Localizer["ManageProductsFor"]: <span style="color: var(--mud-palette-primary); font-weight: 600;">@SelectedOrganization.OrganizationName</span>
                        </MudText>
                    </div>

                    <!-- Add Product Section -->
                    <MudGrid Class="mb-3" AlignItems="End" Spacing="2">
                        <MudItem xs="12">
                            <div style="width: 350px; display: inline-block;">
                                <MudSelect T="Guid?"
                                           @bind-Value="SelectedProductToAdd"
                                           Variant="Variant.Outlined"
                                           Placeholder="Select Product To Add"
                                           Dense="true"
                                           Margin="Margin.Dense"
                                           Class="compact-search-box"
                                           Clearable="true">
                                    @if (AvailableProducts?.Any() == true)
                                    {
                                        @foreach (var product in AvailableProducts)
                                        {
                                            <MudSelectItem T="Guid?" Value="@(product.Id)">
                                                <div class="d-flex align-items-center">
                                                    <MudIcon Icon="@Icons.Material.Filled.Apps" Size="Size.Small" Class="mr-2" Style="color: var(--mud-palette-primary);" />
                                                    <MudText Typo="Typo.body2">@product.Name</MudText>
                                                </div>
                                            </MudSelectItem>
                                        }
                                    }
                                    else
                                    {
                                        <MudSelectItem T="Guid?" Value="null" Disabled="true">
                                            @(AvailableProducts == null ? Localizer["LoadingProducts"] : Localizer["NoProductsAvailable"])
                                        </MudSelectItem>
                                    }
                                </MudSelect>
                            </div>
                            @if (SelectedProductToAdd != null)
                            {
                                <div style="display: inline-block; margin-left: 16px; vertical-align: top; margin-top: 4px;">
                                    <MudButton Variant="Variant.Filled"
                                               StartIcon="@Icons.Material.Filled.Add"
                                               Color="Color.Primary"
                                               Style="min-height: 30px;height: 35px;padding: 2px 16px;font-size: 0.8rem;width: 135px;"
                                               Class="uniform-button"
                                               OnClick="AddProductToOrganization"
                                               Disabled="IsAdding">
                                        @if (IsAdding)
                                        {
                                            <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
                                            <span>@Localizer["Adding"]</span>
                                        }
                                        else
                                        {
                                            <span>@Localizer["AddProduct"]</span>
                                        }
                                    </MudButton>
                                </div>
                            }
                        </MudItem>
                    </MudGrid>

                    <MudDivider Class="mb-3" />

                    <!-- Products Grid Section -->
                    <SfGrid @ref="ProductGrid"
                            DataSource="@Products"
                            AllowPaging="true"
                            AllowSorting="true"
                            GridLines="GridLine.Both"
                            Width="100%">
                        <GridPageSettings PageSize="10" PageSizes="true" />
                        <GridColumns>
                            <GridColumn Field="@nameof(Product.Id)" HeaderText="ID" Visible="false" />
                            <GridColumn Field="@nameof(Product.Name)" HeaderText="@Localizer["ProductName"]" TextAlign="TextAlign.Left" Width="200">
                                <Template>
                                    <div class="d-flex align-items-center">
                                        <MudIcon Icon="@Icons.Material.Filled.Apps" Size="Size.Small" Class="mr-2" Style="color: var(--mud-palette-primary);" />
                                        <span>@((context as Product)?.Name)</span>
                                    </div>
                                </Template>
                            </GridColumn>
                            <GridColumn Field="@nameof(Product.Description)" HeaderText="@Localizer["Description"]" TextAlign="TextAlign.Left" />
                            <GridColumn Field="@nameof(Product.Byproduct)" HeaderText="@Localizer["Byproduct"]" TextAlign="TextAlign.Center" Width="140">
                                <Template>
                                    <MudChip T="string"
                                             Size="Size.Small"
                                             Color="@(string.IsNullOrEmpty((context as Product)?.Byproduct) ? Color.Default : Color.Info)">
                                        @((context as Product)?.Byproduct ?? "N/A")
                                    </MudChip>
                                </Template>
                            </GridColumn>
                            <GridColumn HeaderText="@Localizer["Actions"]" Width="100" TextAlign="TextAlign.Center">
                                <Template>
                                    <MudTooltip Text="@Localizer["RemoveProductAccess"]">
                                        <MudButton Variant="Variant.Text"
                                                   StartIcon="@Icons.Material.Filled.Delete"
                                                   Color="Color.Error"
                                                   Size="Size.Small"
                                                   OnClick="@(() => RemoveProductFromOrganization((Product)context))"
                                                   Disabled="@IsRemoving">
                                            @if (IsRemoving)
                                            {
                                                <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                                            }
                                        </MudButton>
                                    </MudTooltip>
                                </Template>
                            </GridColumn>
                        </GridColumns>
                    </SfGrid>
                </MudPaper>
            </MudItem>
        </MudGrid>
    }

    <!-- Empty State for No Products -->
    @if (SelectedOrganization != null && (Products == null || !Products.Any()))
    {
        <MudGrid>
            <MudItem xs="12">
                <MudPaper Class="pa-4" Elevation="1">
                    <div class="text-center pa-4">
                        <MudIcon Icon="@Icons.Material.Filled.ProductionQuantityLimits" Style="font-size: 4rem; color: #cbd5e1;" />
                        <MudText Typo="Typo.h6" Class="mt-2">@Localizer["NoProductsFound"]</MudText>
                        <MudText Typo="Typo.body2" Class="mt-1">@Localizer["Add Products To Get Started"]</MudText>
                    </div>
                </MudPaper>
            </MudItem>
        </MudGrid>
    }
</GenericCard>

<style>

    /* Ensure the container is exactly 44px tall */
    .compact-search-box .mud-input-control {
        min-height: 48px !important;
        height: 48px !important;
        padding-top: 0 !important;
        padding-bottom: 0 !important;
        display: flex;
        align-items: center;
    }
    /* Adjust the input padding to vertically center text */
    .compact-search-box input {
        padding: 12px 8px !important; /* top-bottom 12px centers text in 48px */
        font-size: 0.85rem !important;
        box-sizing: border-box;
        height: 100%;
    }
    /* Label inside the box (when not focused) */
    .compact-search-box .mud-input-label {
        font-size: 0.9rem !important;
        top: 14px !important;
    }
        /* Label floating when focused or filled */
        .compact-search-box .mud-input-label.mud-shrink {
            font-size: 0.7rem !important;
            top: -6px !important;
        }

</style>
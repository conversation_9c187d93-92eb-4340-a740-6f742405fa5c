﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Localization;
using Microsoft.Identity.Client;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using TeyaWebApp.ViewModel;

namespace TeyaUIViewModels.ViewModel
{
    public class MCSTokenService : ITokenService
    {
        private readonly string _clientId = Environment.GetEnvironmentVariable("AzureAd--ClientId");
        private readonly string _clientSecret = Environment.GetEnvironmentVariable("AUTH-CLIENT-SECRET");
        private readonly string[] _scopes = new[] { Environment.GetEnvironmentVariable("AUTH_SCOPE") };
        private string _accessToken;
        private DateTime _tokenExpiration = DateTime.MinValue;
        private readonly GraphApiService _graphApiService;
        private readonly IStringLocalizer<TokenService> _localizer;
        private readonly NavigationManager _navigationManager;
        private readonly AuthenticationStateProvider _authenticationStateProvider;

        public MCSTokenService(
        GraphApiService graphApiService,
        IStringLocalizer<TokenService> localizer,
        NavigationManager navigationManager,
        AuthenticationStateProvider authenticationStateProvider)
        {
            _graphApiService = graphApiService;
            _localizer = localizer;
            _navigationManager = navigationManager;
            _authenticationStateProvider = authenticationStateProvider;
        }
        public string? AccessToken
        {
            get
            {
                if (string.IsNullOrEmpty(_accessToken) || DateTime.UtcNow >= _tokenExpiration)
                {
                    _accessToken = GetAccessTokenAsync().GetAwaiter().GetResult();
                }
                return _accessToken;
            }
            set => _accessToken = value;
        }

        public string? UserDetails { get; set; }

        public string AccessToken2
        {
            get => AccessToken;
            set => _accessToken = value;
        }
        public string? RefreshToken { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }

        public async Task<string> GetAccessTokenAsync()
        {

            var app = ConfidentialClientApplicationBuilder.Create(_clientId)
                .WithClientSecret(_clientSecret)
                .WithAuthority($"{Environment.GetEnvironmentVariable("AzureAd--Authority")}/v2.0")
                .Build();

            var result = await app.AcquireTokenForClient(_scopes).ExecuteAsync();

            // Store the token and set expiration time (usually result.ExpiresOn)
            _accessToken = result.AccessToken;
            _tokenExpiration = result.ExpiresOn.DateTime.AddMinutes(-5); // Buffer of 5 minutes

            return result.AccessToken;

        }
        public async Task<string?> GetValidatedAccessTokenAsync()
        {
            var token = AccessToken;
            if (string.IsNullOrEmpty(token))
            {
                _navigationManager.NavigateTo(_localizer["/authentication/login"], true);
                return null;
            }
            return token;
        }

        public async Task<string?> GetValidatedAccessToken2Async()
        {
            var token = AccessToken2;
            if (string.IsNullOrEmpty(token))
            {
                _navigationManager.NavigateTo(_localizer["/authentication/login"], true);
                return null;
            }
            return token;
        }

    }
}
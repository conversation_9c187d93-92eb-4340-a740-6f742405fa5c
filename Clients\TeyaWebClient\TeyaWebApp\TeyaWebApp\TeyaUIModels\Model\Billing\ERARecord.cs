﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model.Billing
{
    public class ERARecord
    {
        public Guid Id { get; set; }
        public Guid OrganizationId { get; set; }
        public bool Subscription { get; set; } = false;
        public string? SelectPostingStatus { get; set; } = string.Empty;
        public string? SelectPayer { get; set; } = string.Empty;
        public string? SelectPostedBy { get; set; } = string.Empty;
        public DateTime? SelectPostedDate { get; set; }

        // ERA Process Properties
        public string? ERAType { get; set; } = string.Empty;
        public string? ImportOption { get; set; } = string.Empty;
        public string? Facility { get; set; } = string.Empty;
        public bool MakeDefaultFacility { get; set; }
        public string? PostingStatus { get; set; } = string.Empty;

        public string? PaymentId { get; set; } = string.Empty;
        public string? TransactionType { get; set; } = string.Empty;
        public string? PostedBy { get; set; } = string.Empty;
        public string? TransactionMethod { get; set; } = string.Empty;
        public string? PostedDateText { get; set; } = string.Empty;
        public decimal UnpostedPageAmount { get; set; } = 0.00m;
        public decimal PostedPageAmount { get; set; } = 0.00m;
        public decimal UnpostedTotalAmount { get; set; } = 0.00m;
        public decimal PostedTotalAmount { get; set; } = 0.00m;
        public string? PayerName { get; set; } = string.Empty;
        public DateTime? PaymentDate { get; set; }
        public DateTime? EobDate { get; set; }
        public string? CheckNumber { get; set; } = string.Empty;
        public DateTime? CheckDate { get; set; }
        public decimal CheckAmount { get; set; }
        public decimal PostedAmount { get; set; }
        public decimal BalanceAmount { get; set; }
        public string? ClaimId { get; set; } = string.Empty;

        public string? PaymentPostingId { get; set; } = string.Empty;
        public string? ClaimNumber { get; set; } = string.Empty;
        public string? PatientName { get; set; } = string.Empty;
        public string? ProviderName { get; set; } = string.Empty;
        public string? PaidAmount1 { get; set; } = string.Empty;
        public string? PaymentDate1 { get; set; } = string.Empty;
        public string? PaymentNumber1 { get; set; } = string.Empty;
        public string? PayerName1 { get; set; } = string.Empty;
        public string? PaidAmount2 { get; set; } = string.Empty;
        public string? PaymentDate2 { get; set; } = string.Empty;
        public string? PaymentNumber2 { get; set; } = string.Empty;
        public string? PayerName2 { get; set; } = string.Empty;
        public string? FeeSchedule { get; set; } = string.Empty;
        public bool IsActive { get; set; } = true;
    }
}

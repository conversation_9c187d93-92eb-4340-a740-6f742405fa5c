﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class FlowsheetReading : IModel
    {
        public Guid Id { get; set; }
        public string FlowsheetName { get; set; }
        public string Heading { get; set; }
        public string DateFormat { get; set; }
        public string SortOrder { get; set; }
        public string FreeText { get; set; }
        public bool Immunization { get; set; }
        public string? SerializedData  { get; set; }
        public Guid PatientId { get; set; }
        public bool BloodPressure { get; set; }
        public bool IsEditable { get; set; }
        public bool Pulse { get; set; }
        public bool Temperature { get; set; }
        public bool Height { get; set; }
        public bool Weight { get; set; }
        public bool LabTest { get; set; }
        public bool Medication { get; set; }
        public bool DiTest { get; set; }
        public bool ProcedureTest { get; set; }
        public Guid OrganizationId { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid? UpdatedBy { get; set; }
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime? UpdatedDate { get; set; }
        public bool Subscription { get; set; }
        public bool IsActive { get; set; } = true;
    }
}

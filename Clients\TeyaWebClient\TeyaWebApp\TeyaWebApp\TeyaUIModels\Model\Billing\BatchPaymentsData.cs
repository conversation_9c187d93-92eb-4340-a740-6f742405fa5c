﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model.Billing
{
    public class BatchPaymentsData:IModel
    {
        public Guid Id { get; set; }
        public Guid BatchId { get; set; }
        public bool? Status { get; set; }
        public string Name { get; set; }
        public string CreatedBy { get; set; }
        public DateTime Date { get; set; }
        public string CreatedDate { get; set; }
        public decimal BatchAmount { get; set; }
        public decimal PostedAmount { get; set; }
        public bool? DefaultBatch { get; set; }
        public Guid? OrganizationID { get; set; }
        public bool? Subscription { get; set; }
    }
}



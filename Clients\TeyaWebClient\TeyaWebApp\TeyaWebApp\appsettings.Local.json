﻿{
  "EnvironmentVariables": {

    "AzureAd--ClientId": "e21369d6-92b3-446b-b981-0291bcb29b1b",
    "AzureAd--Authority": "https://TeyaHealthDevAuth.ciamlogin.com/03a052f6-4a19-4ae7-8ed7-47b794e0e597",

    "EncounterNotesURL": "http://localhost/5001",
    "PracticeApiURL": "http://localhost/5002",
    "AppointmentsURL": "http://localhost/5003",
    "MemberServiceURL": "http://localhost/5004",
    "AlertsServiceURL": "http://localhost/5005",
    "BillingURL": "http://localhost/5006",
    "AUTH-POST-LOGOUT-URI": "https://localhost:7256/Home",

    "RedisConnectionString": "TeyaHealthDev-RedisCache.redis.cache.windows.net:6380,password=Cd1YjICbfSaNcu97HYjYQ3dOinHj7JfAKAzCaMGjQlU=,ssl=True,abortConnect=False",
    "RxNormUrl": "https://rxnav.nlm.nih.gov/REST/Prescribe",
    "AudioUrl": "https://teyarecordingsdev.blob.core.windows.net/audiofiles",
    "EMAIL-SERVICE-CONNECTION-STRING": "endpoint=https://emailservice-dev.unitedstates.communication.azure.com/;accesskey=452xhwCL3T6gP7eFUA6nXf8Cb9PTmU6aeJdCPTXMgWRzPemihAzoJQQJ99BAACULyCpSlSHjAAAAAZCS5bjb",
    "APP-LOGIN-URL": "https://dev.teyahealth.com/siginin-oidc",
    "GRAPH-API-BASE-URL": "https://graph.microsoft.com",
    "AZURE-SPEECH-API-KEY": "91SxUNRBf4Q5jx9PUNcFCAXwDjymNH7yoqudOgaYX9vYtWGP0JIZJQQJ99ALACYeBjFXJ3w3AAAYACOGwBTm",
    "AZURE-REGION": "eastus",
    "AZURE-LANGUAGE": "en-US",
    "AZURE-BLOB-CONNECTION-STRING": "DefaultEndpointsProtocol=https;AccountName=teyarecordingsdev;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net",
    "AZURE-BLOB-CONTAINER-NAME": "audiofiles",
    "AUTH-CLIENT-SECRET": "****************************************",
    "AUTH-RESPONSE-TYPE": "code",
    "AUTH-SAVE-TOKENS": "true",
    "AUTH-CALLBACK-PATH": "/signin-oidc",
    "AUTH-SCOPE-1": "openid",
    "AUTH-SCOPE-2": "profile",
    "SyncfusionKey": "MzU3MTQ3NUAzMjM3MmUzMDJlMzBVaUhlNTJjTEcwd00vVGtWWnh1ZmVxSWlMa3IwUlcvM2xlQzF3cklPUXFrPQ==",

    "PrimaryDomain": "TeyaHealthDevAuth.onmicrosoft.com",
    "ServicePrincipleClientId": "d20b72c2-619c-4b74-bb31-2194e8e5a137",
    "ServicePrincipleTenantId": "03a052f6-4a19-4ae7-8ed7-47b794e0e597",
    "ServicePrincipleSecret": "****************************************",
    "EXTENSION-PREFIX": "extension_8a2d87f30a864e7e8b70f49083a6ff68"
  }
}

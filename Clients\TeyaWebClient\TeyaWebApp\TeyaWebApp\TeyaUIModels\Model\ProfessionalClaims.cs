﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class ProfessionalClaims
    {
        public Guid Id { get; set; }
        public string? ClaimNumber { get; set; }
        public string? AppointmentFacility { get; set; }
        public string? POS { get; set; }
        public string? PatientInfo { get; set; }
        public string? Billing { get; set; }
        public string? ClaimStatus { get; set; }
        public string? Rendering { get; set; }
        public string? Supervisor { get; set; }
        public enum Source { FDB, CMS }
        public bool IsActive { get; set; } = true;
        public decimal CoPay { get; set; }
        public decimal PatientUncoveredAmount { get; set; }
        public DateTime? ClaimDate { get; set; }

        public DateTime? ServiceDate { get; set; }
        public decimal PatientCharges { get; set; }
        public decimal PatientPayments { get; set; }
        public decimal PatientBalance { get; set; }
        public decimal TotalCharges { get; set; }
        public decimal TotalPayments { get; set; }
        public decimal TotalBalance { get; set; }
        public string? BillingNotes { get; set; }
        public bool BillToPatient { get; set; }
        public string? Provider1 { get; set; }
        public string? Provider2 { get; set; }
        public string? PaymentRejectionStatus { get; set; }
        public string? Department { get; set; }
        public Guid? OrganizationID { get; set; }
        public bool? Subscription { get; set; }
        public string? ResidentType { get; set; }
        public string? StudentStatus { get; set; }
        public string? EmploymentStatus { get; set; }
        public string? ClaimEditingIndicator { get; set; }
        public string? ClaimType { get; set; }
        public string? FacilityLabId { get; set; }
        public string? FacilityType { get; set; }
        public bool IsResubmittal { get; set; }
        public string? ResubmissionCode { get; set; }
        public string? ResubmissionRefNumber { get; set; }
        public string? Hcfa10d { get; set; }
        public string? Hcfa19 { get; set; }
        public bool SterilizationAbortionNo { get; set; }
        public bool SterilizationAbortionYes { get; set; }
        public bool HealthyKidServiceYes { get; set; }
        public bool HealthyKidServiceNo { get; set; }
        public bool FamilyPlanningYes { get; set; }
        public bool FamilyPlanningNo { get; set; }
        public string? ProviderAssignmentIndicator { get; set; }
        public string? ClaimData { get; set; }
        public string? DelayReason { get; set; }
        public bool EmploymentRelatedYes { get; set; }
        public bool EmploymentRelatedNo { get; set; }
        public bool AccidentAuto { get; set; }
        public bool AccidentNonAuto { get; set; }
        public bool AccidentNo { get; set; }
        public string? AccidentPlace { get; set; }
        public string? AccidentHour { get; set; }
        public string? ExternalCauseOfAccident { get; set; }
        public string? ResponsibilityIndicator { get; set; }
        public string? DocumentationIndicator { get; set; }
        public string? DocumentationType { get; set; }
        public string? AttachmentControlNumber { get; set; }
        public DateTime? DateDocumentationSent { get; set; }
        public string? ReleaseOfInformation { get; set; }
        public DateTime? SignatureDate { get; set; }
        public DateTime? UnableToWorkFromDate { get; set; }
        public DateTime? UnableToWorkToDate { get; set; }
        public DateTime? HospitalizationFromDate { get; set; }
        public DateTime? HospitalizationToDate { get; set; }
        public bool OutsideLabYes { get; set; }
        public bool OutsideLabNo { get; set; }
        public decimal LabCharges { get; set; }
        public string? Symptom { get; set; }
        public DateTime? AccidentSymptomDate { get; set; }
        public DateTime? DateLastSeen { get; set; }
        public DateTime? InitialTreatmentDate { get; set; }
        public string? SimilarSymptom { get; set; }
        public DateTime? SimilarSymptomDate { get; set; }
        public string? SpecialProgramCode { get; set; }
        public string? EPSDTReferralGiven { get; set; }
        public string? EPSDTReferralCode { get; set; }
        public string? ReferringProviderANSI { get; set; }
        public string? ReferringProviderName { get; set; }
        public string? ReferringProviderID { get; set; }
    }
}
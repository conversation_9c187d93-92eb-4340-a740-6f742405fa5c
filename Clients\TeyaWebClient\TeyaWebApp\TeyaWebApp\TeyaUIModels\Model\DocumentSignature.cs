using System;
using System.ComponentModel.DataAnnotations;

namespace TeyaUIModels.Model
{
    public class DocumentSignature : IModel
    {
        public Guid Id { get; set; }
        public Guid RecordId { get; set; }
        public Guid SignerId { get; set; }
        public string SignerName { get; set; }
        public string SignerRole { get; set; }
        public Guid OrganizationId { get; set; }
        public DateTime SignedDate { get; set; }
        public string SignatureId { get; set; }
        public string IPAddress { get; set; }
        public SignatureType Type { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    }

    public enum SignatureType
    {
        Primary = 0,
        CoSign = 1
    }
}

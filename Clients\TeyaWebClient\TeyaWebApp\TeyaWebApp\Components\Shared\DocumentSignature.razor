@using TeyaUIModels.Model
@using TeyaUIViewModels.ViewModel
@using MudBlazor
@inject ISignatureService SignatureService
@inject IMemberService MemberService
@inject ISnackbar Snackbar
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@inject IDialogService DialogService

<div class="document-signature-container">
    <MudCard Class="signature-card" Elevation="2">
        <MudCardHeader>
            <CardHeaderContent>
                <div class="signature-header">
                    <MudIcon Icon="@Icons.Material.Filled.Assignment" Class="signature-icon" />
                    <MudText Typo="Typo.h6">Document Signature</MudText>
                    @if (IsSigned)
                    {
                        <MudChip Color="Color.Success" Size="Size.Small" Class="signed-chip">SIGNED</MudChip>
                    }
                </div>
            </CardHeaderContent>
        </MudCardHeader>

        <MudCardContent>
            @if (!IsSigned)
            {
                <!-- Initial Signing Section -->
                <div class="signing-section">
                    <MudText Typo="Typo.body1" Class="mb-3">Please sign this document to proceed:</MudText>
                    <MudButton Variant="Variant.Filled" 
                               Color="Color.Primary" 
                               StartIcon="@Icons.Material.Filled.Edit"
                               OnClick="SignDocument"
                               Disabled="IsLoading"
                               Class="sign-button">
                        @if (IsLoading)
                        {
                            <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                            <span class="ml-2">Signing...</span>
                        }
                        else
                        {
                            <span>Sign Document</span>
                        }
                    </MudButton>
                </div>
            }
            else
            {
                <!-- Post-Signing Options -->
                <MudExpansionPanels Class="signing-options" Elevation="0">
                    <MudExpansionPanel IsInitiallyExpanded="false" Class="signing-panel">
                        <TitleContent>
                            <div class="panel-title">
                                <MudIcon Icon="@Icons.Material.Filled.Edit" Class="mr-2" />
                                <MudText Typo="Typo.subtitle1">Signing</MudText>
                                <MudSpacer />
                                <MudIconButton Icon="@Icons.Material.Filled.Close" 
                                               Size="Size.Small" 
                                               OnClick="() => {}" 
                                               Class="close-panel-btn" />
                            </div>
                        </TitleContent>
                        <ChildContent>
                            <div class="signing-content">
                                <!-- Action Selection -->
                                <MudText Typo="Typo.subtitle2" Class="mb-3">Choose Action:</MudText>
                                <MudRadioGroup @bind-SelectedOption="SelectedAction" Class="action-radio-group">
                                    <MudRadio Option="SigningAction.SignAgain" Color="Color.Primary">Sign Again</MudRadio>
                                    <MudRadio Option="SigningAction.RequestCoSign" Color="Color.Primary">Request CoSign</MudRadio>
                                </MudRadioGroup>

                                @if (SelectedAction == SigningAction.RequestCoSign)
                                {
                                    <!-- Provider Selection -->
                                    <div class="provider-selection mt-4">
                                        <MudText Typo="Typo.subtitle2" Class="mb-2" Color="Color.Info">
                                            <MudIcon Icon="@Icons.Material.Filled.Person" Size="Size.Small" Class="mr-1" />
                                            Select provider for review
                                        </MudText>
                                        <MudSelect @bind-Value="SelectedProviderId" 
                                                   Label="Select Provider" 
                                                   Variant="Variant.Outlined"
                                                   Class="provider-select">
                                            @foreach (var provider in Providers)
                                            {
                                                <MudSelectItem Value="provider.Id">@provider.UserName</MudSelectItem>
                                            }
                                        </MudSelect>
                                    </div>

                                    <!-- Comments Section -->
                                    @if (Comments.Any())
                                    {
                                        <div class="comments-section mt-4">
                                            <div class="comments-header">
                                                <MudText Typo="Typo.subtitle2" Class="mb-2">Comments (@PendingCommentsCount pending)</MudText>
                                                <MudButton Size="Size.Small"
                                                           Variant="Variant.Outlined"
                                                           Color="Color.Primary"
                                                           OnClick="OpenCommentResolutionDialog"
                                                           StartIcon="@Icons.Material.Filled.Comment"
                                                           Class="resolve-comments-btn">
                                                    Resolve Comments
                                                </MudButton>
                                            </div>
                                            @foreach (var comment in Comments.Take(3))
                                            {
                                                <div class="comment-item">
                                                    <div class="comment-header">
                                                        <MudText Typo="Typo.caption" Color="Color.Primary">@comment.AuthorName</MudText>
                                                        <MudText Typo="Typo.caption" Color="Color.Secondary">@comment.Date.ToString("MM/dd/yyyy HH:mm")</MudText>
                                                    </div>
                                                    <MudText Typo="Typo.body2" Class="comment-text">@comment.Text</MudText>
                                                    @if (!string.IsNullOrEmpty(comment.Status))
                                                    {
                                                        <MudChip Size="Size.Small"
                                                                 Color="@GetStatusColor(comment.Status)"
                                                                 Class="comment-status">
                                                            @comment.Status
                                                        </MudChip>
                                                    }
                                                </div>
                                            }
                                            @if (Comments.Count > 3)
                                            {
                                                <MudText Typo="Typo.caption" Color="Color.Secondary" Class="more-comments">
                                                    And @(Comments.Count - 3) more comments...
                                                </MudText>
                                            }
                                        </div>
                                    }
                                </div>

                                <!-- Action Buttons -->
                                <div class="action-buttons mt-4">
                                    <MudButton Variant="Variant.Outlined" 
                                               Color="Color.Secondary" 
                                               OnClick="Cancel"
                                               Class="cancel-btn">
                                        Cancel
                                    </MudButton>
                                    <MudButton Variant="Variant.Filled" 
                                               Color="Color.Primary" 
                                               OnClick="PerformAction"
                                               Disabled="IsLoading || (SelectedAction == SigningAction.RequestCoSign && SelectedProviderId == Guid.Empty)"
                                               Class="action-btn">
                                        @if (IsLoading)
                                        {
                                            <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                                        }
                                        @GetActionButtonText()
                                    </MudButton>
                                </div>
                            </div>
                        </ChildContent>
                    </MudExpansionPanel>
                </MudExpansionPanels>
            }

            <!-- Signature Information -->
            @if (CurrentSignature != null)
            {
                <div class="signature-info mt-4">
                    <MudDivider Class="mb-3" />
                    <MudText Typo="Typo.caption" Class="signature-details">
                        Electronically signed by: @CurrentSignature.SignerName<br />
                        Date: @CurrentSignature.SignedDate.ToString("MMMM dd, yyyy 'at' h:mm tt")<br />
                        IP Address: @CurrentSignature.IPAddress<br />
                        Signature ID: @CurrentSignature.SignatureId
                    </MudText>
                    <div class="signature-actions mt-2">
                        <MudButton Variant="Variant.Text" 
                                   Size="Size.Small" 
                                   StartIcon="@Icons.Material.Filled.Edit"
                                   OnClick="() => {}"
                                   Class="sign-cosign-btn">
                            Sign/CoSign
                        </MudButton>
                    </div>
                </div>
            }
        </MudCardContent>
    </MudCard>
</div>

@code {
    [Parameter] public Record CurrentRecord { get; set; }
    [Parameter] public Guid OrganizationId { get; set; }
    [Parameter] public bool Subscription { get; set; }
    [Parameter] public EventCallback<bool> OnSignatureChanged { get; set; }

    private bool IsLoading { get; set; }
    private bool IsSigned { get; set; }
    private DocumentSignature CurrentSignature { get; set; }
    private List<Member> Providers { get; set; } = new();
    private SigningAction SelectedAction { get; set; } = SigningAction.SignAgain;
    private Guid SelectedProviderId { get; set; } = Guid.Empty;
    private List<CommentItem> Comments { get; set; } = new();
    private int PendingCommentsCount => Comments.Count(c => c.Status == "Pending");

    public enum SigningAction
    {
        SignAgain,
        RequestCoSign
    }

    public class CommentItem
    {
        public Guid Id { get; set; } = Guid.NewGuid();
        public string AuthorName { get; set; }
        public DateTime Date { get; set; }
        public string Text { get; set; }
        public string Status { get; set; } = "Pending";
        public string SectionName { get; set; }
    }

    protected override async Task OnInitializedAsync()
    {
        await LoadSignatureStatus();
        await LoadProviders();
        await LoadComments();
    }

    protected override async Task OnParametersSetAsync()
    {
        if (CurrentRecord != null)
        {
            await LoadSignatureStatus();
        }
    }

    private async Task LoadSignatureStatus()
    {
        if (CurrentRecord == null) return;

        try
        {
            IsSigned = await SignatureService.HasRecordBeenSignedAsync(CurrentRecord.Id, OrganizationId, Subscription);
            
            if (IsSigned)
            {
                var signatures = await SignatureService.GetSignaturesByRecordIdAsync(CurrentRecord.Id, OrganizationId, Subscription);
                CurrentSignature = signatures.FirstOrDefault(s => s.Type == SignatureType.Primary && s.IsActive);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add("Error loading signature status", Severity.Error);
        }
    }

    private async Task LoadProviders()
    {
        try
        {
            Providers = await MemberService.GetProviderlistAsObjectAsync(OrganizationId, Subscription);
        }
        catch (Exception ex)
        {
            Snackbar.Add("Error loading providers", Severity.Error);
        }
    }

    private async Task LoadComments()
    {
        // Mock comments for now - replace with actual service call
        Comments = new List<CommentItem>
        {
            new CommentItem
            {
                AuthorName = "Dr. Smith",
                Date = DateTime.Now.AddDays(-1),
                Text = "Please review the medication dosage in section 3.",
                Status = "Pending",
                SectionName = "Medications"
            },
            new CommentItem
            {
                AuthorName = "Dr. Johnson",
                Date = DateTime.Now.AddDays(-2),
                Text = "The patient history needs additional details.",
                Status = "Pending",
                SectionName = "History"
            },
            new CommentItem
            {
                AuthorName = "Dr. Wilson",
                Date = DateTime.Now.AddDays(-3),
                Text = "Assessment section looks good.",
                Status = "Resolved",
                SectionName = "Assessment"
            }
        };
    }

    private async Task SignDocument()
    {
        if (CurrentRecord == null) return;

        IsLoading = true;
        try
        {
            var signature = new DocumentSignature
            {
                Id = Guid.NewGuid(),
                RecordId = CurrentRecord.Id,
                SignerId = Guid.Parse("current-user-id"), // Replace with actual user ID
                SignerName = "Dr. Current User", // Replace with actual user name
                SignerRole = "Provider",
                OrganizationId = OrganizationId,
                SignedDate = DateTime.UtcNow,
                SignatureId = $"SIG-{DateTime.UtcNow:yyyyMMdd-HHmmss}-001",
                IPAddress = "*************", // Replace with actual IP
                Type = SignatureType.Primary
            };

            await SignatureService.CreateSignatureAsync(signature, OrganizationId, Subscription);
            await LoadSignatureStatus();
            await OnSignatureChanged.InvokeAsync(true);
            
            Snackbar.Add("Document signed successfully", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add("Error signing document", Severity.Error);
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task PerformAction()
    {
        if (SelectedAction == SigningAction.SignAgain)
        {
            await SignDocument();
        }
        else if (SelectedAction == SigningAction.RequestCoSign)
        {
            await RequestCoSign();
        }
    }

    private async Task RequestCoSign()
    {
        if (SelectedProviderId == Guid.Empty || CurrentRecord == null) return;

        IsLoading = true;
        try
        {
            var selectedProvider = Providers.FirstOrDefault(p => p.Id == SelectedProviderId);
            if (selectedProvider == null) return;

            var request = new SignatureRequest
            {
                Id = Guid.NewGuid(),
                RecordId = CurrentRecord.Id,
                PatientId = CurrentRecord.PatientId,
                PatientName = CurrentRecord.PatientName,
                PatientAge = 0, // Calculate from patient data
                PatientGender = "Unknown", // Get from patient data
                RequesterId = Guid.Parse("current-user-id"), // Replace with actual user ID
                RequesterName = "Dr. Current User", // Replace with actual user name
                ReviewerId = SelectedProviderId,
                ReviewerName = selectedProvider.UserName,
                OrganizationId = OrganizationId,
                RequestDate = DateTime.UtcNow,
                Status = SignatureRequestStatus.Pending,
                Comments = string.Empty
            };

            await SignatureService.CreateSignatureRequestAsync(request, OrganizationId, Subscription);
            Snackbar.Add("CoSign request sent successfully", Severity.Success);
            
            // Reset form
            SelectedAction = SigningAction.SignAgain;
            SelectedProviderId = Guid.Empty;
        }
        catch (Exception ex)
        {
            Snackbar.Add("Error sending CoSign request", Severity.Error);
        }
        finally
        {
            IsLoading = false;
        }
    }

    private void Cancel()
    {
        SelectedAction = SigningAction.SignAgain;
        SelectedProviderId = Guid.Empty;
    }

    private string GetActionButtonText()
    {
        return SelectedAction == SigningAction.SignAgain ? "Sign" : "Send";
    }

    private async Task OpenCommentResolutionDialog()
    {
        var parameters = new DialogParameters
        {
            ["Comments"] = Comments,
            ["OnCommentsResolved"] = EventCallback.Factory.Create<List<CommentItem>>(this, HandleCommentsResolved)
        };

        var options = new DialogOptions
        {
            CloseButton = true,
            MaxWidth = MaxWidth.Large,
            FullWidth = true
        };

        var dialog = await DialogService.ShowAsync<CommentResolutionDialog>("Resolve Comments", parameters, options);
        var result = await dialog.Result;

        if (!result.Cancelled)
        {
            StateHasChanged();
        }
    }

    private async Task HandleCommentsResolved(List<CommentItem> resolvedComments)
    {
        Comments = resolvedComments;
        await InvokeAsync(StateHasChanged);
    }

    private Color GetStatusColor(string status)
    {
        return status?.ToLower() switch
        {
            "resolved" => Color.Success,
            "pending" => Color.Warning,
            "rejected" => Color.Error,
            _ => Color.Default
        };
    }
}

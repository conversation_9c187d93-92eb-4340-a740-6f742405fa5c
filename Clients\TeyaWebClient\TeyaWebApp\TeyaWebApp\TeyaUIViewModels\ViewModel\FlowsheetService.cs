﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using DotNetEnv;
using System.Text;
using System.Text.Json;
using Newtonsoft.Json;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text;
using Azure.Core;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaUIViewModels.TeyaUIViewModelResources;
using Microsoft.Graph.Models;

namespace TeyaUIViewModels.ViewModel
{
    public class FlowsheetService : IFlowsheetService
    {
        private readonly HttpClient _httpClient;
        private readonly IStringLocalizer<FlowsheetService> _localizer;
        private readonly ILogger<FlowsheetService> _logger;
        private readonly string _EncounterNotes;
        private readonly ITokenService _tokenService;

        public FlowsheetService(HttpClient httpClient, IConfiguration configuration, IStringLocalizer<FlowsheetService> localizer, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            Env.Load();
            _EncounterNotes = Environment.GetEnvironmentVariable("EncounterNotesURL");
            _tokenService = tokenService;
        }

        /// <summary>
        ///  Get Active Family Member by Id 
        /// </summary>
        public async Task<List<Flowsheet>> GetFlowsheetByIdAsyncAndIsActive(Guid id, Guid? OrgID, bool Subscription)
        {
            var apiUrl = $"{_EncounterNotes}/api/FamilyMember/{id}/isActive/{OrgID}/{Subscription}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<Flowsheet>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["AddressRetrievalFailure"]);
            }
        }

        /// <summary>
        ///  Get All Family Member by Id 
        /// </summary>
        public async Task<List<Flowsheet>> GetFlowsheetAsync(Guid id, Guid? OrgID, bool Subscription)
        {
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            var apiUrl = $"{_EncounterNotes}/api/Flowsheet/{id}/{OrgID}/{Subscription}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<List<Flowsheet>>();
            }
            else
            {
                throw new HttpRequestException(_localizer["MemberRetrievalFailure"]);
            }
        }

        /// <summary>
        /// Add new Member
        /// </summary>
        public async Task CreateFlowsheetAsync(List<Flowsheet> flowsheet, Guid? OrgID, bool Subscription)
        {
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(flowsheet);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
            var apiUrl = $"{_EncounterNotes}/api/Flowsheet/{OrgID}/{Subscription}";
            var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
            {
                Content = content
            };

            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            var response = await _httpClient.SendAsync(requestMessage);

            if (response.IsSuccessStatusCode)
            {
                var responseBody = await response.Content.ReadAsStringAsync();
            }
            else
            {
                var error = await response.Content.ReadAsStringAsync();
                throw new HttpRequestException(_localizer["FlowsheetRetrievalFailure"]);
            }
        }

        /// <summary>
        /// Update an existing member
        /// </summary>
        public async Task UpdateFlowsheetAsync(Flowsheet flowsheet, Guid? OrgID, bool Subscription)
        {
            var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
            var apiUrl = $"{_EncounterNotes}/api/Flowsheet/{flowsheet.Id}/{OrgID}/{Subscription}";
            var bodyContent = System.Text.Json.JsonSerializer.Serialize(flowsheet);
            var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

            var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl);
            requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            requestMessage.Content = content;

            var response = await _httpClient.SendAsync(requestMessage);
            response.EnsureSuccessStatusCode();
        }

        /// <summary>
        ///  Delete an existing Member By Id
        /// </summary>
        public async Task DeleteFlowsheetAsync(Guid MemberId, Guid? OrgID, bool Subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_EncounterNotes}/api/Flowsheet/{MemberId}/{OrgID}/{Subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();
            }
            catch (Exception ex)
            {
                throw new Exception(_localizer["Error"], ex);
            }
        }

        /// <summary>
        ///  Update an existing List of Family Members
        /// </summary>
        public async Task UpdateFlowsheetList(List<Flowsheet> flowsheet, Guid? OrgID, bool Subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_EncounterNotes}/api/Flowsheet/updateFlowsheet/{OrgID}/{Subscription}";

                var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var bodyContent = System.Text.Json.JsonSerializer.Serialize(flowsheet);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
                requestMessage.Content = content;

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();
            }
            catch (Exception ex)
            {
                throw new HttpRequestException(_localizer["UpdateFamilyMemberListFailure"], ex);
            }
        }
    }
}
using System;
using System.ComponentModel.DataAnnotations;

namespace TeyaUIModels.Model
{
    public class SignatureRequest : IModel
    {
        public Guid Id { get; set; }
        public Guid RecordId { get; set; }
        public Guid PatientId { get; set; }
        public string PatientName { get; set; }
        public int PatientAge { get; set; }
        public string PatientGender { get; set; }
        public Guid RequesterId { get; set; }
        public string RequesterName { get; set; }
        public Guid ReviewerId { get; set; }
        public string ReviewerName { get; set; }
        public Guid OrganizationId { get; set; }
        public DateTime RequestDate { get; set; }
        public DateTime? ActionDate { get; set; }
        public SignatureRequestStatus Status { get; set; }
        public string Comments { get; set; }
        public string ReviewerComments { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        public DateTime? UpdatedDate { get; set; }
    }

    public enum SignatureRequestStatus
    {
        Pending = 0,
        Approved = 1,
        Rejected = 2,
        ChangesRequested = 3
    }
}

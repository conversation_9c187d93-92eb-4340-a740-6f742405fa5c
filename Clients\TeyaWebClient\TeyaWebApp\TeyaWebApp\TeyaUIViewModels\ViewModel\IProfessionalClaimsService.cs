﻿using System.Net.Http;
using TeyaUIModels.Model;

namespace TeyaUIViewModels.ViewModel
{
    public interface IProfessionalClaimsService
    {
        Task<List<CompleteProfessionalClaims>> GetProfessionalClaimsByIdAsync(Guid id, Guid? OrgID, bool Subscription);
        Task<List<CompleteProfessionalClaims>> GetAllProfessionalClaimsAsync(Guid? OrgID, bool Subscription);
        Task AddProfessionalClaimsAsync(List<CompleteProfessionalClaims> completeProfessionalClaims, Guid? OrgID, bool Subscription);
        Task UpdateProfessionalClaimsAsync(CompleteProfessionalClaims ProfessionalClaims, Guid? OrgID, bool Subscription);
        Task DeleteProfessionalClaimsAsync(CompleteProfessionalClaims ProfessionalClaims, Guid id, Guid? OrgID, bool Subscription);


    }
}
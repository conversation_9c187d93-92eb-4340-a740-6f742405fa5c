﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class CompleteProfessionalClaims
    {
        public Guid Id { get; set; }
        public ProfessionalClaims professionalclaims { get; set; }
        public List<ProfessionalClaimsICD> professionalClaimsICD { get; set; }
        public List<ProfessionalClaimsCPT> professionalClaimsCPT { get; set; }
        public List<ProfessionalClaimsImmunization> professionalClaimsImmunization { get; set; }
        public List<ProfessionalClaimsPayments> professionalClaimsPayments { get; set; }
        public Guid? OrganizationID { get; set; }
        public bool? Subscription { get; set; }
    }
}

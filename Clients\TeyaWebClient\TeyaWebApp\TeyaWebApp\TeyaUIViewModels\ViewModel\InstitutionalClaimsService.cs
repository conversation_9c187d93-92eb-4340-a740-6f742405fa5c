﻿using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;

namespace TeyaUIViewModels.ViewModel
{
    public class InstitutionalClaimsService : IInstitutionalClaimsService
    {
        private readonly ITokenService _tokenService;
        private readonly HttpClient _httpClient;
        private readonly string _BillingService;
        private readonly IStringLocalizer<InstitutionalClaimsService> _localizer;
        private readonly ILogger<InstitutionalClaimsService> _logger;

        public InstitutionalClaimsService(HttpClient httpClient, IStringLocalizer<InstitutionalClaimsService> localizer, ILogger<InstitutionalClaimsService> logger, ITokenService tokenService)
        {
            _httpClient = httpClient;
            _localizer = localizer;
            _logger = logger;
            _BillingService = Environment.GetEnvironmentVariable("BillingURL");
            _tokenService = tokenService;
        }

        // Fixed method name to match controller expectation
        public async Task<IEnumerable<InstitutionalClaim>> GetAllInstitutionalClaimAsync(Guid orgId, bool subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_BillingService}/api/InstitutionalClaims/{orgId}/{subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                response.EnsureSuccessStatusCode();

                var responseData = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };

                return JsonSerializer.Deserialize<IEnumerable<InstitutionalClaim>>(responseData, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingAllInstitutionalClaimss"]);
                throw;
            }
        }

        public async Task<InstitutionalClaim> GetInstitutionalClaimByIdAsync(Guid id, Guid orgId, bool subscription)
        {
            try
            {
                var apiUrl = $"{_BillingService}/api/InstitutionalClaims/{id}/{orgId}/{subscription}";
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };

                    return JsonSerializer.Deserialize<InstitutionalClaim>(responseData, options);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"API Request Failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["GetByIdFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingInstitutionalClaimsById"], id);
                throw;
            }
        }

        // Fixed method signature to match controller call
        public async Task AddInstitutionalClaimsAsync(InstitutionalClaim institutionalClaims, Guid orgId, bool subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var bodyContent = JsonSerializer.Serialize(institutionalClaims);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");
                var apiUrl = $"{_BillingService}/api/InstitutionalClaims/{orgId}/{subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, apiUrl)
                {
                    Content = content
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    var responseData = await response.Content.ReadAsStringAsync();
                    _logger.LogInformation(_localizer["API Response: {ResponseData}"], responseData);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Add failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["AddInstitutionalClaimsFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorAddingInstitutionalClaims"]);
                throw;
            }
        }

        // Fixed method signature to match controller call
        public async Task UpdateInstitutionalClaimsAsync(InstitutionalClaim institutionalClaims, Guid id, Guid orgId, bool subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_BillingService}/api/InstitutionalClaims/{id}/{orgId}/{subscription}";
                var bodyContent = JsonSerializer.Serialize(institutionalClaims);
                var content = new StringContent(bodyContent, Encoding.UTF8, "application/json");

                var requestMessage = new HttpRequestMessage(HttpMethod.Put, apiUrl)
                {
                    Content = content
                };
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["InstitutionalClaimsUpdatedSuccessfully"], institutionalClaims.ID);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Update failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["InstitutionalClaimsUpdateFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorUpdatingInstitutionalClaims"]);
                throw;
            }
        }

        // Fixed method name to match controller call
        public async Task DeleteInstitutionalClaimsAsync(Guid id, Guid orgId, bool subscription)
        {
            try
            {
                var accessToken = await _tokenService.GetValidatedAccessTokenAsync();
                var apiUrl = $"{_BillingService}/api/InstitutionalClaims/{id}/{orgId}/{subscription}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Delete, apiUrl);
                requestMessage.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation(_localizer["InstitutionalClaimsDeletedSuccessfully"], id);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError($"Delete failed. Status Code: {response.StatusCode}, Response: {errorContent}");
                    throw new HttpRequestException(_localizer["InstitutionalClaimsDeletionFailed"]);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorDeletingInstitutionalClaims"], id);
                throw;
            }
        }
    }
}
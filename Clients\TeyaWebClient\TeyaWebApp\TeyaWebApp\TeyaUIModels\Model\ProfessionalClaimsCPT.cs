﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model
{
    public class ProfessionalClaimsCPT
    {
        public Guid CPTId { get; set; }
        public bool IsSelected { get; set; }
        public string? Code { get; set; } = string.Empty;
        public string? Description { get; set; } = string.Empty;
        public string? POS { get; set; } = string.Empty;
        public string? TOS { get; set; } = string.Empty;
        public DateTime? SDOS { get; set; }
        public DateTime? EDOS { get; set; }
        public string? M1 { get; set; } = string.Empty;
        public string? M2 { get; set; } = string.Empty;
        public string? M3 { get; set; } = string.Empty;
        public string? ICD1 { get; set; } = string.Empty;
        public string? ICD2 { get; set; } = string.Empty;
        public string? ICD3 { get; set; } = string.Empty;
        public string? ICD4 { get; set; } = string.Empty;
        public int Units { get; set; }
        public int SerialNumber { get; set; }
        public decimal BilledFee { get; set; }
        public string? PcpId { get; set; } = string.Empty;
        public Guid ProfessionalClaimsId { get; set; }
    }
}

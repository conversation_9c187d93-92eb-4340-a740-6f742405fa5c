﻿@using Microsoft.AspNetCore.Components.Authorization
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject IDialogService DialogService
@inject NavigationManager NavigationManager
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@implements IDisposable

<head>
    <link href="../app.css" rel="stylesheet" />
</head>

<MudPaper Width="@(IsDrawerOpen ? "240px" : "72px")" Class="py-3" Elevation="0" Style="@(IsDrawerOpen ? "" : "margin-left: 0; padding: 12px 6px;")">
    <MudNavMenu>

        @if (HasEHRProductAccess() && (IsEHRSelected() || !HasBillingProductAccess()))
        {
            @if (IsPageAccessible(Chart))
            {
                @if (IsDrawerOpen)
                {
                    <MudNavLink Class="mud-navlink-style" Href="/chart" Icon="@Icons.Material.Filled.Assignment">
                        @Localizer["Chart"]
                    </MudNavLink>
                }
                else
                {
                    <div class="collapsed-nav-item">
                        <MudTooltip Text="@Localizer["Chart"]" Placement="Placement.Right">
                            <MudIconButton Icon="@Icons.Material.Filled.Assignment"
                                           OnClick="@(() => NavigateToPage("/chart"))"
                                           Color="Color.Inherit"
                                           Size="Size.Medium" />
                        </MudTooltip>
                        <span class="collapsed-nav-label">@Localizer["Chart"]</span>
                    </div>
                }
            }

            @if (IsPageAccessible(Appointments))
            {
                @if (IsDrawerOpen)
                {
                    <MudNavLink Class="mud-navlink-style" Href="/appointments" Icon="@Icons.Material.Filled.Event">
                        @Localizer["Appointments"]
                    </MudNavLink>
                }
                else
                {
                    <div class="collapsed-nav-item">
                        <MudTooltip Text="@Localizer["Appointments"]" Placement="Placement.Right">
                            <MudIconButton Icon="@Icons.Material.Filled.Event"
                                           OnClick="@(() => NavigateToPage("/appointments"))"
                                           Color="Color.Inherit"
                                           Size="Size.Medium" />
                        </MudTooltip>
                        <span class="collapsed-nav-label">@Localizer["Appointments"]</span>
                    </div>
                }
            }

            @* @if (IsPageAccessible(OrderSets)) *@
            @* { *@
            @*     @if (IsDrawerOpen) *@
            @*     { *@
            @*         <MudNavLink Class="mud-navlink-style" Href="/ordersets" Icon="@Icons.Material.Filled.ListAlt"> *@
            @*             @Localizer["OrderSets"] *@
            @*         </MudNavLink> *@
            @*     } *@
            @*     else *@
            @*     { *@
            @*         <div class="collapsed-nav-item"> *@
            @*             <MudTooltip Text="@Localizer["OrderSets"]" Placement="Placement.Right"> *@
            @*                 <MudIconButton Icon="@Icons.Material.Filled.ListAlt"  *@
            @*                                OnClick="@(() => NavigateToPage("/ordersets"))" *@
            @*                                Color="Color.Inherit" *@
            @*                                Size="Size.Medium" /> *@
            @*             </MudTooltip> *@
            @*             <span class="collapsed-nav-label">@Localizer["OrderSets"]</span> *@
            @*         </div> *@
            @*     } *@
            @* } *@

            @if (IsPageAccessible(Practice))
            {
                @if (IsDrawerOpen)
                {
                    <MudNavGroup Title="@Localizer["Practice"]" Expanded="true" Icon="@Icons.Material.Filled.Sports" Class="mud-navgroup-style">
                        <MudNavLink Class="mud-navlink-style" Href="/officevisit" Icon="@Icons.Material.Filled.MedicalServices">
                            @Localizer["OfficeVisit"]
                        </MudNavLink>
                        <MudNavLink Class="mud-navlink-style" Href="/patients" Icon="@Icons.Material.Filled.People">
                            @Localizer["Patient"]
                        </MudNavLink>
                    </MudNavGroup>
                }
                else
                {
                    <div class="collapsed-nav-item">
                        <MudTooltip Text="@Localizer["OfficeVisit"]" Placement="Placement.Right">
                            <MudIconButton Icon="@Icons.Material.Filled.MedicalServices" 
                                           OnClick="@(() => NavigateToPage("/officevisit"))"
                                           Color="Color.Inherit"
                                           Size="Size.Medium" />
                        </MudTooltip>
                        <span class="collapsed-nav-label">@Localizer["OfficeVisit"]</span>
                    </div>
                }
            }

            @if (ShouldShowEHRSettings())
            {
                @if (IsDrawerOpen)
                {
                    <MudNavGroup Class="mud-navgroup-style" Title="@Localizer["Settings"]" Expanded="true" Icon="@Icons.Material.Filled.Settings">

                        @* @if (IsPageAccessible(Patients))
                        {
                            <MudNavLink Class="mud-navlink-style" Href="/patients" Icon="@Icons.Material.Filled.People">
                                @Localizer["Patient"]
                            </MudNavLink>
                        } *@

                        @if (ShouldRenderLicenseLink)
                        {
                            @if (IsPageAccessible(LicenseActivation))
                            {
                                <MudNavGroup Class="mud-navgroup-style" Title="@Localizer["LicenseActivation"]" Expanded="true" Icon="@Icons.Material.Filled.Verified">
                                    @if (IsPageAccessible(License))
                                    {
                                        <MudNavLink Class="mud-navlink-style" Href="/license" Icon="@Icons.Material.Filled.Approval">
                                            @Localizer["License"]
                                        </MudNavLink>
                                    }
                                    @if (IsPageAccessible(ProductFeatureSettings))
                                    {
                                        <MudNavLink Class="mud-navlink-style" Href="/productfeaturesettings" Icon="@Icons.Material.Filled.ProductionQuantityLimits">
                                            @Localizer["ProductFeatures"]
                                        </MudNavLink>
                                    }
                                </MudNavGroup>
                            }
                            @if (IsPageAccessible(Security))
                            {
                                <MudNavLink Class="mud-navlink-style" Href="/security" Icon="@Icons.Material.Filled.VerifiedUser">
                                    @Localizer["Security"]
                                </MudNavLink>
                            }
                        }

                        @if (IsPageAccessible(UserManagement))
                        {
                            <MudNavLink Class="mud-navlink-style" Href="/usermanagement" Icon="@Icons.Material.Filled.VerifiedUser">
                                @Localizer["UserManagement"]
                            </MudNavLink>
                        }

                        @if (IsPageAccessible(Templates))
                        {
                            <MudNavLink Class="mud-navlink-style" Href="/templates" Icon="@Icons.Material.Filled.DocumentScanner">
                                @Localizer["Templates"]
                            </MudNavLink>
                        }
                        
                        @if (IsPageAccessible(Config))
                        {
                            <MudNavLink Class="mud-navlink-style" Href="/config" Icon="@Icons.Material.Filled.AdminPanelSettings">
                                @Localizer["Config"]
                            </MudNavLink>
                        }
                        
                    </MudNavGroup>
                }
                else
                {
                    <!-- When collapsed, show individual icons for settings items -->
                    @if (IsPageAccessible(Patients))
                    {
                        <div class="collapsed-nav-item">
                            <MudTooltip Text="@Localizer["Patient"]" Placement="Placement.Right">
                                <MudIconButton Icon="@Icons.Material.Filled.People" 
                                               OnClick="@(() => NavigateToPage("/patients"))"
                                               Color="Color.Inherit"
                                               Size="Size.Medium" />
                            </MudTooltip>
                            <span class="collapsed-nav-label">@Localizer["Patient"]</span>
                        </div>
                    }

                    @if (ShouldRenderLicenseLink)
                    {
                        @if (IsPageAccessible(License))
                        {
                            <div class="collapsed-nav-item">
                                <MudTooltip Text="@Localizer["License"]" Placement="Placement.Right">
                                    <MudIconButton Icon="@Icons.Material.Filled.Approval" 
                                                   OnClick="@(() => NavigateToPage("/license"))"
                                                   Color="Color.Inherit"
                                                   Size="Size.Medium" />
                                </MudTooltip>
                                <span class="collapsed-nav-label">@Localizer["License"]</span>
                            </div>
                        }
                        @if (IsPageAccessible(ProductFeatureSettings))
                        {
                            <div class="collapsed-nav-item">
                                <MudTooltip Text="@Localizer["ProductFeatures"]" Placement="Placement.Right">
                                    <MudIconButton Icon="@Icons.Material.Filled.ProductionQuantityLimits" 
                                                   OnClick="@(() => NavigateToPage("/productfeaturesettings"))"
                                                   Color="Color.Inherit"
                                                   Size="Size.Medium" />
                                </MudTooltip>
                                <span class="collapsed-nav-label">@Localizer["Product Features"]</span>
                            </div>
                        }
                        @if (IsPageAccessible(Security))
                        {
                            <div class="collapsed-nav-item">
                                <MudTooltip Text="@Localizer["Security"]" Placement="Placement.Right">
                                    <MudIconButton Icon="@Icons.Material.Filled.VerifiedUser" 
                                                   OnClick="@(() => NavigateToPage("/security"))"
                                                   Color="Color.Inherit"
                                                   Size="Size.Medium" />
                                </MudTooltip>
                                <span class="collapsed-nav-label">@Localizer["Security"]</span>
                            </div>
                        }
                    }

                    @if (IsPageAccessible(UserManagement))
                    {
                        <div class="collapsed-nav-item">
                            <MudTooltip Text="@Localizer["UserManagement"]" Placement="Placement.Right">
                                <MudIconButton Icon="@Icons.Material.Filled.VerifiedUser" 
                                               OnClick="@(() => NavigateToPage("/usermanagement"))"
                                               Color="Color.Inherit"
                                               Size="Size.Medium" />
                            </MudTooltip>
                            <span class="collapsed-nav-label">@Localizer["UserManagement"]</span>
                        </div>
                    }

                    @if (IsPageAccessible(Templates))
                    {
                        <div class="collapsed-nav-item">
                            <MudTooltip Text="@Localizer["Templates"]" Placement="Placement.Right">
                                <MudIconButton Icon="@Icons.Material.Filled.DocumentScanner" 
                                               OnClick="@(() => NavigateToPage("/templates"))"
                                               Color="Color.Inherit"
                                               Size="Size.Medium" />
                            </MudTooltip>
                            <span class="collapsed-nav-label">@Localizer["Templates"]</span>
                        </div>
                    }
                    
                    @if (IsPageAccessible(Config))
                    {
                        <div class="collapsed-nav-item">
                            <MudTooltip Text="@Localizer["Config"]" Placement="Placement.Right">
                                <MudIconButton Icon="@Icons.Material.Filled.AdminPanelSettings" 
                                               OnClick="@(() => NavigateToPage("/config"))"
                                               Color="Color.Inherit"
                                               Size="Size.Medium" />
                            </MudTooltip>
                            <span class="collapsed-nav-label">@Localizer["Config"]</span>
                        </div>
                    }
                }
            }
        }
        
        @if (IsBillingSelected() && HasBillingProductAccess())
        {
            @if (IsPageAccessible(ClaimsLookup))
            {
                @if (IsDrawerOpen)
                {
                    <MudNavLink Class="mud-navlink-style" Href="/claimslookup" Icon="@Icons.Material.Filled.Search">
                        @Localizer["ClaimsLookup"]
                    </MudNavLink>
                }
                else
                {
                    <div class="collapsed-nav-item">
                        <MudTooltip Text="@Localizer["ClaimsLookup"]" Placement="Placement.Right">
                            <MudIconButton Icon="@Icons.Material.Filled.Search" 
                                           OnClick="@(() => NavigateToPage("/claimslookup"))"
                                           Color="Color.Inherit"
                                           Size="Size.Medium" />
                        </MudTooltip>
                        <span class="collapsed-nav-label">@Localizer["ClaimsLookup"]</span>
                    </div>
                }
            }
            @if (IsPageAccessible(PatientDentalClaims))
            {
                @if (IsDrawerOpen)
                {
                    <MudNavLink Class="mud-navlink-style" Href="/dentalclaims" Icon="@Icons.Material.Filled.Assignment">
                        @Localizer["DentalClaims"]
                    </MudNavLink>
                }
                else
                {
                    <div class="collapsed-nav-item">
                        <MudTooltip Text="@Localizer["DentalClaims"]" Placement="Placement.Right">
                            <MudIconButton Icon="@Icons.Material.Filled.Assignment" 
                                           OnClick="@(() => NavigateToPage("/dentalclaims"))"
                                           Color="Color.Inherit"
                                           Size="Size.Medium" />
                        </MudTooltip>
                        <span class="collapsed-nav-label">@Localizer["DentalClaims"]</span>
                    </div>
                }
            }
            @if (IsPageAccessible(PatientERA))
            {
                @if (IsDrawerOpen)
                {
                    <MudNavLink Class="mud-navlink-style" Href="/era" Icon="@Icons.Material.Filled.Assignment">
                        @Localizer["ERA"]
                    </MudNavLink>
                }
                else
                {
                    <div class="collapsed-nav-item">
                        <MudTooltip Text="@Localizer["ERA"]" Placement="Placement.Right">
                            <MudIconButton Icon="@Icons.Material.Filled.Assignment" 
                                           OnClick="@(() => NavigateToPage("/era"))"
                                           Color="Color.Inherit"
                                           Size="Size.Medium" />
                        </MudTooltip>
                        <span class="collapsed-nav-label">@Localizer["ERA"]</span>
                    </div>
                }
            }
            @if (IsPageAccessible(PatientProfessionalClaims))
            {
                @if (IsDrawerOpen)
                {
                    <MudNavLink Class="mud-navlink-style" Href="/professionalclaims" Icon="@Icons.Material.Filled.Assignment">
                        @Localizer["ProfessionalClaims"]
                    </MudNavLink>
                }
                else
                {
                    <div class="collapsed-nav-item">
                        <MudTooltip Text="@Localizer["ProfessionalClaims"]" Placement="Placement.Right">
                            <MudIconButton Icon="@Icons.Material.Filled.Assignment"
                                           OnClick="@(() => NavigateToPage("/professionalclaims"))"
                                           Color="Color.Inherit"
                                           Size="Size.Medium" />
                        </MudTooltip>
                        <span class="collapsed-nav-label">@Localizer["ProfessionalClaims"]</span>
                    </div>
                }
            }

            @if (IsPageAccessible(BillingEncounters))
            {
                @if (IsDrawerOpen)
                {
                    <MudNavLink Class="mud-navlink-style" Href="/billingencounters" Icon="@Icons.Material.Filled.Assignment">
                        @Localizer["Encounters"]
                    </MudNavLink>
                }
                else
                {
                    <div class="collapsed-nav-item">
                        <MudTooltip Text="@Localizer["Encounters"]" Placement="Placement.Right">
                            <MudIconButton Icon="@Icons.Material.Filled.Assignment" 
                                           OnClick="@(() => NavigateToPage("/billingencounters"))"
                                           Color="Color.Inherit"
                                           Size="Size.Medium" />
                        </MudTooltip>
                        <span class="collapsed-nav-label">@Localizer["Encounters"]</span>
                    </div>
                }
            }
            @if (IsPageAccessible(BillingPayments))
            {
                @if (IsDrawerOpen)
                {
                    <MudNavLink Class="mud-navlink-style" Href="/BillingPayments" Icon="@Icons.Material.Filled.Assignment">
                        @Localizer["Payments"]
                    </MudNavLink>
                }
                else
                {
                    <div class="collapsed-nav-item">
                        <MudTooltip Text="@Localizer["Payments"]" Placement="Placement.Right">
                            <MudIconButton Icon="@Icons.Material.Filled.Assignment" 
                                           OnClick="@(() => NavigateToPage("/BillingPayments"))"
                                           Color="Color.Inherit"
                                           Size="Size.Medium" />
                        </MudTooltip>
                        <span class="collapsed-nav-label">@Localizer["Payments"]</span>
                    </div>
                }
            }

            @if (IsPageAccessible(InstitutionalClaims))
            {
                @if (IsDrawerOpen)
                {
                    <MudNavLink Class="mud-navlink-style" Href="/institutionalclaims" Icon="@Icons.Material.Filled.Receipt">
                        @Localizer["InstitutionalClaims"]
                    </MudNavLink>
                }
                else
                {
                    <div class="collapsed-nav-item">
                        <MudTooltip Text="@Localizer["InstitutionalClaims"]" Placement="Placement.Right">
                            <MudIconButton Icon="@Icons.Material.Filled.Receipt"
                                           OnClick="@(() => NavigateToPage("/institutionalclaims"))"
                                           Color="Color.Inherit"
                                           Size="Size.Medium" />
                        </MudTooltip>
                        <span class="collapsed-nav-label">@Localizer["InstitutionalClaims"]</span>
                    </div>
                }
            }
        }

        @if (!_isLoading && !HasEHRProductAccess() && !HasBillingProductAccess() && IsDrawerOpen)
        {
            <MudAlert Severity="Severity.Warning" Class="ma-2">
                <AlertContent>
                    <MudText Typo="Typo.body2">
                        @Localizer["NoProductsAvailable"]
                    </MudText>
                    <MudText Typo="Typo.caption">
                        @Localizer["ContactAdministrator"]
                    </MudText>
                </AlertContent>
            </MudAlert>
        }
    </MudNavMenu>
</MudPaper>

<style>
    .collapsed-nav-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        width: 60px;
        min-height: 70px;
        margin: 4px auto;
        padding: 6px 4px;
        border-radius: 8px;
        transition: background-color 0.2s ease;
        box-sizing: border-box;
    }

    .collapsed-nav-item:hover {
        background-color: rgba(0, 0, 0, 0.04);
    }

    .collapsed-nav-label {
        font-size: 8px;
        line-height: 1.1;
        margin-top: 3px;
        color: rgba(0, 0, 0, 0.7);
        text-align: center;
        white-space: normal;
        overflow: visible;
        text-overflow: unset;
        max-width: 52px;
        min-height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        word-break: break-word;
        hyphens: auto;
    }

    .mud-navgroup-style {
        font-size: 14px !important;
        font-weight: 600 !important;
        color: #344563 !important;
    }

    /* Override MudBlazor styles for collapsed nav */
    .collapsed-nav-item .mud-icon-button {
        margin: 0 !important;
        padding: 8px !important;
    }

    /* Ensure proper spacing for the entire navigation */
    .mud-nav-menu {
        padding: 0 !important;
    }

    .mud-nav-menu .mud-nav-item {
        margin-bottom: 2px;
    }
</style>
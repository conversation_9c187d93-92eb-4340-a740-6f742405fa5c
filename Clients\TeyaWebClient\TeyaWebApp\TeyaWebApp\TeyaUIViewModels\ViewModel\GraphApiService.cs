using Microsoft.Extensions.Localization;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using Microsoft.Graph.Me.SendMail;
using Microsoft.Graph.Models;
using Microsoft.Graph;
using Azure.Core;
using Microsoft.AspNetCore.Components;
using Azure;
using DotNetEnv;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
namespace TeyaWebApp.ViewModel
{
    public class GraphApiService
    {
        private readonly HttpClient _httpClient;
        private readonly ITokenService _tokenService;
        private readonly IStringLocalizer<GraphApiService> _localizer;
        private readonly ActiveUser _activeUser;
        private string _userId;
        private readonly string _baseUrl;
        private readonly ILogger<GraphApiService> _logger;
        private readonly IGraphAdminService _adminService;
        private readonly string _extensionIdOrgName;
        private readonly string _extensionIdAddress;
        public string UserDetails { get; set; }
        public ActiveUser ActiveUser => _activeUser;
        private readonly JsonSerializerOptions _jsonOptions;

        public GraphApiService(HttpClient httpClient, ITokenService tokenService, ILogger<GraphApiService> logger, IStringLocalizer<GraphApiService> localizer, ActiveUser activeUser, IGraphAdminService adminService, IOptions<JsonSerializerOptions> jsonOptions)
        {
            Env.Load();
            _httpClient = httpClient;
            _tokenService = tokenService;
            _localizer = localizer;
            _activeUser = activeUser;
            _baseUrl = $"{Environment.GetEnvironmentVariable("GRAPH-API-BASE-URL")}/v1.0/";
            _logger = logger;
            _adminService = adminService;
            _extensionIdOrgName = $"{Environment.GetEnvironmentVariable("EXTENSION-PREFIX")}_OrganizationName";
            _extensionIdAddress = $"{Environment.GetEnvironmentVariable("EXTENSION-PREFIX")}_Address";
            _jsonOptions = jsonOptions.Value;
        }
        public async Task<bool> UpdateUserProfileAsync(string userId, Dictionary<string, object> updateFields)
        {
            var accessToken = await _adminService.GetAdminAccessTokenAsync();
            if (string.IsNullOrEmpty(accessToken))
                throw new Exception(_localizer["AccessTokenMissing"]);

            try
            {
                var requestUrl = $"{_baseUrl}users/{userId}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Patch, requestUrl)
                {
                    Content = new StringContent(JsonSerializer.Serialize(updateFields), Encoding.UTF8, "application/json")
                };

                requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                if (!response.IsSuccessStatusCode)
                {
                    var error = await response.Content.ReadAsStringAsync();
                    throw new Exception(_localizer["FailedToUpdateUserProfile", response.StatusCode]);
                }

                return true;
            }
            catch (Exception ex)
            {
                throw new Exception(_localizer["ErrorUpdatingUserProfile", ex.Message]);
            }
        }

        public async Task GetLoggedInUserDetailsAsync()
        {
            var accessToken = await _tokenService.GetValidatedAccessToken2Async();

            if (string.IsNullOrEmpty(accessToken))
                throw new Exception("Access token is missing");

            try
            {
                var requestUrl = $"{_baseUrl}me";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, requestUrl);
                requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                if (!response.IsSuccessStatusCode)
                    throw new Exception("Failed to get user details");

                var userId = JsonDocument.Parse(await response.Content.ReadAsStringAsync()).RootElement.GetProperty("id").GetString();

                var userDetailsUrl = $"{_baseUrl}users/{userId}?$select=id,displayName,givenName,surname,userType,jobTitle,companyName,department,officeLocation,streetAddress,city,state,postalCode,country,businessPhones,{_extensionIdOrgName},{_extensionIdAddress},mobilePhone,mail";

                requestMessage = new HttpRequestMessage(HttpMethod.Get, userDetailsUrl);


                requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                response = await _httpClient.SendAsync(requestMessage);
                if (!response.IsSuccessStatusCode)
                    throw new Exception("Failed to get full user details");

                var userDetailsJson = await response.Content.ReadAsStringAsync();
                var activeUser = JsonSerializer.Deserialize<ActiveUser>(userDetailsJson, _jsonOptions);
                _activeUser.id = activeUser.id;
                _activeUser.displayName = activeUser.displayName;
                _activeUser.givenName = activeUser.givenName;
                _activeUser.surname = activeUser.surname;
                _activeUser.userType = activeUser.userType;
                _activeUser.jobTitle = activeUser.jobTitle;
                _activeUser.companyName = activeUser.companyName;
                _activeUser.department = activeUser.department;
                _activeUser.officeLocation = activeUser.officeLocation;
                _activeUser.streetAddress = activeUser.streetAddress;
                _activeUser.city = activeUser.city;
                _activeUser.state = activeUser.state;
                _activeUser.postalCode = activeUser.postalCode;
                _activeUser.country = activeUser.country;
                _activeUser.businessPhones = activeUser.businessPhones;
                _activeUser.mobilePhone = activeUser.mobilePhone;
                _activeUser.mail = activeUser.mail;
                _activeUser.role = activeUser.role;
                _activeUser.OrganizationName = activeUser.OrganizationName;
                _activeUser.Address = activeUser.Address;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error StackTrace: {ex.StackTrace}");
                _logger.LogError($"Error InnerException: {ex.InnerException}");
                throw new Exception(_localizer["ErrorGettingUserDetails", ex.Message]);
            }
        }

        public async Task<bool> GetUserDetailsAsync()
        {

            var accessToken =await _tokenService.GetValidatedAccessToken2Async();

            if (string.IsNullOrEmpty(accessToken))
            {
                throw new Exception(_localizer["AccessTokenMissing"]);
            }

            try
            {
                var requestUrl = $"{_baseUrl}me";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, requestUrl);
                requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);
                Console.Write(response.Content);
                if (!response.IsSuccessStatusCode)
                {
                    throw new Exception(_localizer["FailedToGetUserDetails", response.StatusCode]);
                }
                _userId = JsonDocument.Parse(await response.Content.ReadAsStringAsync()).RootElement.GetProperty("id").GetString();
                return await GetFullUserDetailsAsync(_userId);
            }
            catch (Exception ex)
            {
                throw new Exception(_localizer["ErrorGettingUserDetails", ex.Message]);
            }
        }
        public async Task<bool> GetFullUserDetailsAsync(string userId)
        {
            var accessToken = await _adminService.GetAdminAccessTokenAsync();
            if (string.IsNullOrEmpty(accessToken))
            {
                throw new Exception(_localizer["AccessTokenMissing"]);
            }
            try
            {
                var requestUrl = $"{_baseUrl}users/{userId}?$select=id,displayName,givenName,surname,userType,jobTitle,companyName,department,officeLocation,streetAddress,city,state,postalCode,country,businessPhones,{_extensionIdOrgName},{_extensionIdAddress},mobilePhone,mail";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, requestUrl);
                requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
                var response = await _httpClient.SendAsync(requestMessage);
                if (!response.IsSuccessStatusCode)
                    throw new Exception(_localizer["FailedToGetFullUserDetails", response.StatusCode]);
                UserDetails = await response.Content.ReadAsStringAsync();
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception(_localizer["ErrorGettingFullUserDetails", ex.Message]);
            }
        }
        public async Task<string> GetFullUserAsync(string userId)
        {
            var accessToken = await _adminService.GetAdminAccessTokenAsync();
            if (string.IsNullOrEmpty(accessToken))
                throw new Exception(_localizer["AccessTokenMissing"]);
            try
            {
                var requestUrl = $"{_baseUrl}users/{userId}?$select=id,displayName,givenName,surname,userType,jobTitle,companyName,department,officeLocation,streetAddress,city,state,postalCode,country,businessPhones,mobilePhone,mail";
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, requestUrl);
                requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
                var response = await _httpClient.SendAsync(requestMessage);
                if (!response.IsSuccessStatusCode)
                {
                    throw new Exception(_localizer["FailedToGetFullUserDetails", response.StatusCode]);
                }
                UserDetails = await response.Content.ReadAsStringAsync();
                return UserDetails;
            }
            catch (Exception ex)
            {
                throw new Exception(_localizer["ErrorGettingFullUserDetails", ex.Message]);
            }
        }
        public async Task<bool> DeleteUserAsync()
        {
            var accessToken = await _adminService.GetAdminAccessTokenAsync();
            if (string.IsNullOrEmpty(accessToken))
            {
                throw new Exception(_localizer["AccessTokenMissing"]);
            }
            if (string.IsNullOrEmpty(_userId))
            {
                throw new Exception(_localizer["UserIdNotAvailable"]);
            }
            try
            {
                var requestUrl = $"{_baseUrl}users/{_userId}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Delete, requestUrl);
                requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
                var response = await _httpClient.SendAsync(requestMessage);
                if (!response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    throw new Exception(_localizer["FailedToDeleteUser", response.StatusCode]);
                }
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception(_localizer["ErrorDeletingUser", ex.Message]);
            }
        }
        
        
        public async Task<string?> CreateUserAsync2(
        string displayName,
        string mail,
        string password,
        string signInType,
        string issuer,
        string issuerAssignedId,
        string role = ""
        )
        {
            var accessToken = await _adminService.GetAdminAccessTokenAsync();
            if (string.IsNullOrEmpty(accessToken))
                throw new Exception(_localizer["AccessTokenMissing"]);

            try
            {
                var requestUrl = $"{_baseUrl}users";

                var newUser = new
                {
                    accountEnabled = true,
                    displayName,
                    mail,
                    passwordProfile = new
                    {
                        forceChangePasswordNextSignIn = true,
                        password
                    },
                    identities = new[] {
                new {
                    signInType,
                    issuer,
                    issuerAssignedId
                }
            }
                };

                var jsonBody = JsonSerializer.Serialize(newUser);

                var requestMessage = new HttpRequestMessage(HttpMethod.Post, requestUrl)
                {
                    Content = new StringContent(jsonBody, System.Text.Encoding.UTF8, "application/json")
                };
                requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                var response = await _httpClient.SendAsync(requestMessage);

                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return null;
                }

                var responseContent = await response.Content.ReadAsStringAsync();
                var jsonResponse = JsonSerializer.Deserialize<JsonElement>(responseContent);

                if (!jsonResponse.TryGetProperty("id", out var idProperty))
                {
                    _logger.LogInformation(_localizer["UserIdNotFound"]);
                    return null;
                }

                var createdUserId = idProperty.GetString();
                //if (!string.IsNullOrEmpty(role) && role.Equals("admin", StringComparison.OrdinalIgnoreCase))
                //{
                //    var roleAssigned = await AssignUserAdministratorRoleAsync(createdUserId);
                //    return roleAssigned ? createdUserId : null;
                //}

                return createdUserId;
            }
            catch (Exception ex)
            {
                _logger.LogError(_localizer["ErrorCreatingUser"]);
                return null;
            }
        }

        
       
        public async Task<bool> UpdateUserAsync(UpdatedUserViewModel updatedUserProfile)
        {
            var accessToken = await _adminService.GetAdminAccessTokenAsync();
            if (string.IsNullOrEmpty(accessToken))
            {
                throw new Exception(_localizer["AccessTokenMissing"]);
            }

            try
            {
                var requestUrl = $"{_baseUrl}users/{_userId}";
                var userUpdateData = new
                {
                    displayName = updatedUserProfile.DisplayName,
                    givenName = updatedUserProfile.GivenName,
                    surname = updatedUserProfile.Surname,
                    mail = updatedUserProfile.Mail,
                    userType = updatedUserProfile.UserType,
                    jobTitle = updatedUserProfile.JobTitle,
                    companyName = updatedUserProfile.CompanyName,
                    department = updatedUserProfile.Department,
                    officeLocation = updatedUserProfile.OfficeLocation,
                    mobilePhone = updatedUserProfile.MobilePhone,
                    streetAddress = updatedUserProfile.StreetAddress,
                    city = updatedUserProfile.City,
                    state = updatedUserProfile.State,
                    postalCode = updatedUserProfile.PostalCode,
                    country = updatedUserProfile.Country,
                };
                var jsonBody = JsonSerializer.Serialize(userUpdateData);
                var requestMessage = new HttpRequestMessage(HttpMethod.Patch, requestUrl)
                {
                    Headers = { Authorization = new AuthenticationHeaderValue("Bearer", accessToken) },
                    Content = new StringContent(jsonBody, System.Text.Encoding.UTF8, "application/json")
                };
                var response = await _httpClient.SendAsync(requestMessage);
                if (!response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    throw new Exception(_localizer["FailedToUpdateUser", response.StatusCode]);
                }
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception(_localizer["ErrorUpdatingUser", ex.Message]);
            }
        }

        public async Task<bool> UpdateAsync(string userId, UpdatedUserViewModel updatedUser)
        {
            if (string.IsNullOrEmpty(userId))
                throw new ArgumentException("User ID cannot be null or empty", nameof(userId));
            var accessToken = await _adminService.GetAdminAccessTokenAsync();
            if (string.IsNullOrEmpty(accessToken))
                throw new Exception(_localizer["AccessTokenMissing"]);
            try
            {
                var requestUrl = $"{_baseUrl}users/{userId}";
                var userUpdateData = new
                {
                    displayName = updatedUser.DisplayName,
                    givenName = updatedUser.GivenName,
                    surname = updatedUser.Surname,
                    mail = updatedUser.Mail,
                    userType = updatedUser.UserType,
                    jobTitle = updatedUser.JobTitle,
                    companyName = updatedUser.CompanyName,
                    department = updatedUser.Department,
                    officeLocation = updatedUser.OfficeLocation,
                    mobilePhone = updatedUser.MobilePhone,
                    streetAddress = updatedUser.StreetAddress,
                    city = updatedUser.City,
                    state = updatedUser.State,
                    postalCode = updatedUser.PostalCode,
                    country = updatedUser.Country,
                };
                var jsonBody = JsonSerializer.Serialize(userUpdateData);
                var requestMessage = new HttpRequestMessage(HttpMethod.Patch, requestUrl)
                {
                    Headers = { Authorization = new AuthenticationHeaderValue("Bearer", accessToken) },
                    Content = new StringContent(jsonBody, System.Text.Encoding.UTF8, "application/json")
                };
                var response = await _httpClient.SendAsync(requestMessage);
                if (!response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    throw new Exception(_localizer["FailedToUpdateUser", response.StatusCode]);
                }
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception(_localizer["ErrorUpdatingUser", ex.Message]);
            }
        }

        public async Task<bool> DeleteUserAsync(string userId)
        {
            var accessToken = await _adminService.GetAdminAccessTokenAsync();
            if (string.IsNullOrEmpty(accessToken))
            {
                throw new Exception(_localizer["AccessTokenMissing"]);
            }
            if (string.IsNullOrEmpty(userId))
            {
                throw new Exception(_localizer["UserIdNotProvided"]);
            }
            try
            {
                var requestUrl = $"{_baseUrl}users/{userId}";
                var requestMessage = new HttpRequestMessage(HttpMethod.Delete, requestUrl);
                requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
                var response = await _httpClient.SendAsync(requestMessage);
                if (!response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    throw new Exception(_localizer["FailedToDeleteUser", response.StatusCode]);
                }
                return true;
            }
            catch (Exception ex)
            {
                throw new Exception(_localizer["ErrorDeletingUser", ex.Message]);
            }
        }
        
    }
}

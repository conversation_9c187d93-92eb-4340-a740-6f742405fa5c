﻿namespace TeyaUIViewModels.ViewModel
{
    public interface ITokenService
    {
        string? AccessToken { get; set; }
        string? AccessToken2 { get; set; }
        string? RefreshToken { get; set; }
        string? UserDetails { get; set; }

        Task<string?> GetValidatedAccessTokenAsync();
        Task<string?> GetValidatedAccessToken2Async();
        Task<string> GetAccessTokenAsync();
    }
}
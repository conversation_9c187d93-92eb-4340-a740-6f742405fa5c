﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System.IdentityModel.Tokens.Jwt;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Net.Http;

namespace TeyaUIViewModels.ViewModel
{
    public class TokenService : ITokenService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IStringLocalizer<TokenService> _localizer;
        private readonly NavigationManager _navigationManager;
        private readonly ILogger<TokenService> _logger;
        private readonly HttpClient _httpClient;

        public TokenService(
            IHttpContextAccessor httpContextAccessor,
            IStringLocalizer<TokenService> localizer,
            NavigationManager navigationManager,
            ILogger<TokenService> logger,
            HttpClient httpClient)
        {
            _httpContextAccessor = httpContextAccessor;
            _localizer = localizer;
            _navigationManager = navigationManager;
            _logger = logger;
            _httpClient = httpClient;
        }

        // Token properties
        public string? AccessToken
        {
            get => _httpContextAccessor.HttpContext?.Session.GetString("AccessToken");
            set => _httpContextAccessor.HttpContext?.Session.SetString("AccessToken", value ?? "");
        }

        public string? AccessToken2
        {
            get => _httpContextAccessor.HttpContext?.Session.GetString("AccessToken2");
            set => _httpContextAccessor.HttpContext?.Session.SetString("AccessToken2", value ?? "");
        }

        public string? RefreshToken
        {
            get => _httpContextAccessor.HttpContext?.Session.GetString("RefreshToken");
            set => _httpContextAccessor.HttpContext?.Session.SetString("RefreshToken", value ?? "");
        }

        public string? UserDetails
        {
            get => _httpContextAccessor.HttpContext?.Session.GetString("UserDetails");
            set => _httpContextAccessor.HttpContext?.Session.SetString("UserDetails", value ?? "");
        }

        private bool IsTokenExpired(string token)
        {
            try
            {
                var jwtToken = new JwtSecurityTokenHandler().ReadJwtToken(token);
                // Check if token expires within the next 5 minutes
                return jwtToken.ValidTo <= DateTime.UtcNow.AddMinutes(5);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to parse JWT token");
                return true; // Assume expired if we can't parse it
            }
        }

        private async Task<TokenResponse?> RefreshTokenAsync(string refreshToken, string scope)
        {
            try
            {
                var authority = Environment.GetEnvironmentVariable("AzureAd--Authority");
                var clientId = Environment.GetEnvironmentVariable("AzureAd--ClientId");
                var clientSecret = Environment.GetEnvironmentVariable("AUTH-CLIENT-SECRET");

                _logger.LogInformation($"Attempting token refresh with scope: {scope}");
                _logger.LogInformation($"Authority: {authority}");
                _logger.LogInformation($"ClientId: {clientId}");
                _logger.LogInformation($"Has refresh token: {!string.IsNullOrEmpty(refreshToken)}");
                _logger.LogInformation($"Has client secret: {!string.IsNullOrEmpty(clientSecret)}");

                var tokenEndpoint = $"{authority}/oauth2/v2.0/token";

                var requestBody = new Dictionary<string, string>
                {
                    {"grant_type", "refresh_token"},
                    {"refresh_token", refreshToken},
                    {"client_id", clientId},
                    {"client_secret", clientSecret},
                    {"scope", scope}
                };

                var content = new FormUrlEncodedContent(requestBody);

                // Add headers
                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("Accept", "application/json");

                var response = await _httpClient.PostAsync(tokenEndpoint, content);
                var responseContent = await response.Content.ReadAsStringAsync();

                _logger.LogInformation($"Token refresh response status: {response.StatusCode}");
                _logger.LogInformation($"Token refresh response content: {responseContent}");

                if (response.IsSuccessStatusCode)
                {
                    try
                    {
                        // Log the raw response to see the actual JSON structure
                        _logger.LogInformation($"Raw JSON response: {responseContent}");

                        var tokenResponse = JsonSerializer.Deserialize<TokenResponse>(responseContent, new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true
                        });

                        _logger.LogInformation($"Parsed token response - AccessToken null: {tokenResponse?.AccessToken == null}");
                        _logger.LogInformation($"Parsed token response - RefreshToken null: {tokenResponse?.RefreshToken == null}");

                        return tokenResponse;
                    }
                    catch (JsonException jsonEx)
                    {
                        _logger.LogError(jsonEx, $"Failed to parse JSON response: {responseContent}");
                        return null;
                    }
                }
                else
                {
                    _logger.LogError($"Token refresh failed: {response.StatusCode}, {responseContent}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to refresh token");
                return null;
            }
        }

        private async Task<string?> RefreshUserAccessTokenAsync()
        {
            try
            {
                var refreshToken = RefreshToken;
                if (string.IsNullOrEmpty(refreshToken))
                {
                    _logger.LogWarning("No refresh token available for user access token");
                    return null;
                }

                var scope = $"api://{Environment.GetEnvironmentVariable("AzureAd--ClientId")}/access_as_user";
                var tokenResponse = await RefreshTokenAsync(refreshToken, scope);

                if (tokenResponse?.AccessToken != null)
                {
                    AccessToken = tokenResponse.AccessToken;

                    // Update refresh token if a new one was provided
                    if (!string.IsNullOrEmpty(tokenResponse.RefreshToken))
                    {
                        RefreshToken = tokenResponse.RefreshToken;
                    }

                    return tokenResponse.AccessToken;
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to refresh user access token");
                return null;
            }
        }

        private async Task<string?> RefreshGraphAccessTokenAsync()
        {
            try
            {
                var refreshToken = RefreshToken;
                if (string.IsNullOrEmpty(refreshToken))
                {
                    _logger.LogWarning("No refresh token available for graph access token");
                    return null;
                }

                var scope = $"{Environment.GetEnvironmentVariable("GRAPH-API-BASE-URL")}/.default";
                var tokenResponse = await RefreshTokenAsync(refreshToken, scope);

                if (tokenResponse?.AccessToken != null)
                {
                    AccessToken2 = tokenResponse.AccessToken;

                    // Update refresh token if a new one was provided
                    if (!string.IsNullOrEmpty(tokenResponse.RefreshToken))
                    {
                        RefreshToken = tokenResponse.RefreshToken;
                    }

                    return tokenResponse.AccessToken;
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to refresh graph access token");
                return null;
            }
        }

        public async Task<string?> GetValidatedAccessTokenAsync()
        {
            var token = AccessToken;
            if (string.IsNullOrEmpty(token))
            {
                _logger.LogWarning("No access token found");
                _navigationManager.NavigateTo(_localizer["/authentication/login"], true);
                return null;
            }

            if (IsTokenExpired(token))
            {
                _logger.LogInformation("Access token is expired or about to expire, attempting refresh");
                var refreshedToken = await RefreshUserAccessTokenAsync();
                _logger.LogInformation("Access token REFRESHED");
                if (string.IsNullOrEmpty(refreshedToken))
                {
                    _logger.LogWarning("Failed to refresh access token, redirecting to login");
                    _navigationManager.NavigateTo(_localizer["/authentication/login"], true);
                    return null;
                }

                return refreshedToken;
            }

            return token;
        }

        public async Task<string?> GetValidatedAccessToken2Async()
        {
            var token = AccessToken2;
            if (string.IsNullOrEmpty(token))
            {
                _logger.LogWarning("No access token 2 found");
                _navigationManager.NavigateTo(_localizer["/authentication/login"], true);
                return null;
            }

            if (IsTokenExpired(token))
            {
                _logger.LogInformation("Access token 2 is expired or about to expire, attempting refresh");
                var refreshedToken = await RefreshGraphAccessTokenAsync();
                _logger.LogInformation("Access token 2 REFRESHED");
                if (string.IsNullOrEmpty(refreshedToken))
                {
                    _logger.LogWarning("Failed to refresh access token 2, redirecting to login");
                    _navigationManager.NavigateTo(_localizer["/authentication/login"], true);
                    return null;
                }

                return refreshedToken;
            }

            return token;
        }

        public Task<string> GetAccessTokenAsync()
        {
            throw new NotImplementedException();
        }
    }

    // Helper class for token response
    public class TokenResponse
    {
        [JsonPropertyName("access_token")]
        public string? AccessToken { get; set; }

        [JsonPropertyName("refresh_token")]
        public string? RefreshToken { get; set; }

        [JsonPropertyName("token_type")]
        public string? TokenType { get; set; }

        [JsonPropertyName("expires_in")]
        public int ExpiresIn { get; set; }

        [JsonPropertyName("scope")]
        public string? Scope { get; set; }

        [JsonPropertyName("id_token")]
        public string? IdToken { get; set; }
    }
}
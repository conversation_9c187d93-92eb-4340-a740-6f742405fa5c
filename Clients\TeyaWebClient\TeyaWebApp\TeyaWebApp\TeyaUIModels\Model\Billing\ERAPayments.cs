﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaUIModels.Model.Billing
{
    public class ERAPayments
    {
        public Guid PaymentId { get; set; }
        public bool IsSelected { get; set; }
        public string? Status { get; set; }
        public string? Payer { get; set; }
        public string? PostedBy { get; set; }
        public DateTime PostedDate { get; set; }
        public string? Method { get; set; }
        public DateTime Dated { get; set; }
        public string? TraceNumber { get; set; }
        public decimal Amount { get; set; }
        public Guid ERAId { get; set; }
    }
}

﻿using Markdig;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.Azure.Amqp.Framing;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Options;
using Microsoft.Graph.Models;
using Microsoft.JSInterop;
using MudBlazor;
using Syncfusion.Blazor.RichTextEditor;
using Syncfusion.Windows.Shared.Resources;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.RegularExpressions;
using System.Timers;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Components.Layout;
using TeyaWebApp.Services;
using TeyaWebApp.TeyaAIScribeResources;
using TeyaWebApp.ViewModel;
using Unity;
using static Microsoft.Azure.Amqp.CbsConstants;

namespace TeyaWebApp.Components.Pages
{
    public partial class Notes : ComponentBase
    {
        private bool isLoading { get; set; }
        private Random random = new Random();
        private bool isPaused = false;
        private System.Threading.Timer animationTimer;
        private bool AICard { get; set; }
        private bool toggleCard = true;
        private bool productVisibility;
        private bool componentRefreshKey = false;   //test key for refresh
        private Guid currentRecordId { get; set; }
        private string currentPatientName { get; set; }
        private Guid currentPatientId { get; set; }
        private bool PatientCreationFlag { get; set; } = false;
        private bool showPatientCreationForm { get; set; } = false;
        [Inject]
        private ILogger<Notes> logger { get; set; }
        private List<Record> records = new();
        private List<Member> AllOrgMembers = new();
        private List<Dictionary<string, Dictionary<string, string>>> NotesData = new List<Dictionary<string, Dictionary<string, string>>>();
        [Inject] ITemplateService templateService { get; set; }
        private HashSet<string> sectionNames = new(); // HashSet to prevent duplicates
        private HashSet<string> RenderedSections = new(); // Tracks rendered UI sections
        private List<TemplateData> ProviderData = new();

        private MudDialog MicrophoneDialog;
        private Type componentType { get; set; }
        [Inject]
        private ISoapNotesComponentsService SoapNotesComponentsService { get; set; }
        [Inject]
        private ILogger<Notes> Logger { get; set; }
        private IEnumerable<SoapNotesComponent> ListDetails = new List<SoapNotesComponent>();
        [Inject]
        private ActiveUser User { get; set; }
        private string data_value { get; set; } = String.Empty;
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        //[Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }

        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        private Guid? OrgID { get; set; }

        private Dictionary<string, Dictionary<string, string>> parsedNotes;
        private bool hasChanges = false;
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private ISignatureService SignatureService { get; set; }
      
        [Parameter] public EventCallback OnGoBackToEncounters { get; set; }
        [Parameter] public EventCallback OnClearSelectedNoteId { get; set; }
        [Parameter] public bool ShowSingleRecordView { get; set; }

        private const int MRNLength = 8;
        private bool NewRecord { get; set; }

        private Dictionary<string, bool> isEditingDict = new Dictionary<string, bool>();
        private Dictionary<string, string> editorContents = new Dictionary<string, string>();
        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>()
        {
        new ToolbarItemModel() { Command = ToolbarCommand.Bold },
        new ToolbarItemModel() { Command = ToolbarCommand.Italic },
        new ToolbarItemModel() { Command = ToolbarCommand.Underline },
        new ToolbarItemModel() { Command = ToolbarCommand.FontName },
        new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
        new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
        new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
        new ToolbarItemModel() { Command = ToolbarCommand.Undo },
        new ToolbarItemModel() { Command = ToolbarCommand.Redo },
        new ToolbarItemModel() { Name = "close" },
        };
        private async Task OpenEdit(string editorKey)
        {
            isEditingDict[editorKey] = true;
            await Task.Delay(50); // Small delay to ensure editor is rendered
        }
        private async Task CloseRTE(string editorKey)
        {
            isEditingDict[editorKey] = false;
        }
        private string editorContent;
        [Inject]
        private PatientService _PatientService { get; set; }
        private Guid? PatientID { get; set; }
        private string TemplateName { get; set; }
        private bool isAnimating = false;
        private bool isRecorderActive = false;
        private bool isPausedd = false;
        private IEnumerable<Speech> Speeches = new List<Speech>();
        private bool isDataLoading = true;

        private string? CurrentSpeechText { get; set; }
        private string? PatientId { get; set; }
        private string MicIcon => isRecorderActive ? Localizer["stop_circle"] : Localizer["mic"];
        private string PauseResumeIcon => isPausedd ? Localizer["play_arrow"] : Localizer["pause"];
        private System.Timers.Timer callDurationTimer;
        private int callDuration = 0;
        private bool isAudioDetected = false;
        public Member member = new Member();
        public TeyaUIModels.Model.Address address = new TeyaUIModels.Model.Address();
        private Insurance insurance = new Insurance();
        private Guardian guardian = new Guardian();
        private Employer employer = new Employer();
        //private List<CompleteOrderSet> resolveOrderset;
        private Record record;
        public bool flag { get; set; } = false;
        [Inject] IJSRuntime JS { get; set; }
        [Inject] UserContext UserContext { get; set; }  

        [Parameter] public Guid selectedNoteId { get; set; }
        [Parameter] public EventCallback<Guid> OnSwitchToNotePage { get; set; }

        private bool showSingleRecordView = false;
        private Member patient = new Member();

        private readonly string[] speakerColorPalette = new[] {
        "#4285F4", // Blue
        "#EA4335", // Red
        "#34A853", // Green
        "#FBBC05", // Yellow
        "#8E24AA", // Purple
        "#00ACC1", // Cyan
        "#FB8C00", // Orange
        "#607D8B", // Blue Grey
        "#D81B60", // Pink
        "#1E88E5", // Light Blue
        "#43A047", // Light Green
        "#6D4C41"  // Brown
    };

        private string GetSpeakerColor(int index)
        {
            return speakerColorPalette[index % speakerColorPalette.Length];
        }
        private string GetBubbleColor(string color)
        {
            if (color.StartsWith("#"))
            {
                try
                {
                    return $"{color}33";
                }
                catch
                {
                    return "#f5f5f5";
                }
            }
            return "#f5f5f5";
        }
        private bool isLoadingPatient = true;

        private Guid? appId { get; set; }
        private Guid? PcpID { get; set; }
        protected override async Task OnInitializedAsync()
        {
            // Always show create patient button when no patient data exists
            isLoadingPatient = true;
            PatientCreationFlag = _PatientService.PatientData == null;
            productVisibility = true;   //check product visibility of Provider, Need to be done
            ListDetails = await SoapNotesComponentsService.GetAllDetailsAsync();
            OrgID = UserContext.ActiveUserOrganizationID;
            activeUserOrganizationId = UserContext.ActiveUserOrganizationID;
            Subscription = UserContext.ActiveUserSubscription;
            ProviderData = await templateService.GetTemplatesByPCPIdAsync(Guid.Parse(User.id), OrgID, Subscription);

            AllOrgMembers = await MemberService.GetAllMembersAsync(OrgID ?? Guid.Empty, Subscription);
            AllOrgMembers = AllOrgMembers.Where(m => m.OrganizationID == OrgID).ToList();
            

            if (_PatientService.PatientData != null)
            {
                PatientID = _PatientService.PatientData.Id;
                //defaultTemplate = ProviderData.FirstOrDefault(t => t.IsDefault == true && t.VisitType == _PatientService.VisitType);
                OrgID = _PatientService.PatientData.OrganizationID;
               
                appId = _PatientService.AppointmentsId;
                PcpID= _PatientService.PCPId;
            }
            isLoadingPatient = false;

            if (selectedNoteId != Guid.Empty)
            {
                var singleRecord = await ProgressNotesService.GetRecordByIdAsync(selectedNoteId, OrgID, Subscription);
                if (singleRecord != null)
                {
                    records = new List<Record> { singleRecord };
                    showSingleRecordView = true;
                }
                else
                {
                    Snackbar.Add(Localizer["RecordNotFound"], Severity.Error);
                    showSingleRecordView = false;
                }
            }

            else
            {
                if (_PatientService.PatientData != null)
                {
                    PatientID = _PatientService.PatientData.Id;
                    records = await ProgressNotesService.GetRecordsByPatientIdAsync(PatientID.Value,OrgID,Subscription);
                }
                else
                {
                    records = await ProgressNotesService.GetRecordsByPCPIdAsync(Guid.Parse(User.id),OrgID,Subscription);
                }
                records = records.Where(record => record.isEditable).OrderByDescending(record => record.DateTime).ToList();

            }
            await Task.Delay(100); 
            isDataLoading = false;

            record = records.FirstOrDefault();

            if (record != null)
            {
                patient = await MemberService.GetMemberByIdAsync(record.PatientId, (Guid)OrgID, Subscription);
            }

           
        }

        public string CleanHtml(string rawHtml)
        {
            if (string.IsNullOrEmpty(rawHtml))
                return string.Empty;
            return Regex.Replace(rawHtml, @"<h4[^>]*>.*?<\/h4>", "", RegexOptions.IgnoreCase | RegexOptions.Singleline, TimeSpan.FromSeconds(1));
        }
        public List<Dictionary<string, Dictionary<string, string>>> ExtractNotesData(string recordNotes)
        {
            if (string.IsNullOrEmpty(recordNotes))
                return new List<Dictionary<string, Dictionary<string, string>>>();

            try
            {
                // Clean the JSON string
                string cleanedJson = recordNotes
                    .Replace("record.Notes = ", "")
                    .Replace("}{", "},{")
                    .Trim();

                // Wrap in array if not already
                if (!cleanedJson.StartsWith("["))
                    cleanedJson = $"[{cleanedJson}]";

                // Direct deserialization using System.Text.Json
                return JsonSerializer.Deserialize<List<Dictionary<string, Dictionary<string, string>>>>(cleanedJson)
                       ?? new List<Dictionary<string, Dictionary<string, string>>>();
            }
            catch
            {
                return new List<Dictionary<string, Dictionary<string, string>>>();
            }
        }


        private string ConvertToHtml(string markdown)
        {
            var pipeline = new MarkdownPipelineBuilder().UseAdvancedExtensions().Build();
            return Markdown.ToHtml(markdown, pipeline);
        }

        [JSInvokable]
        public async Task OnAudioDetected(bool isAudioDetect)
        {
            isAudioDetected = isAudioDetect;
            await InvokeAsync(StateHasChanged);
        }
        private string GetBarStyle(int index)
        {
            if (isPaused)
            {
                return "--height: 25px; --opacity: 0.5;";
            }

            if (!isAudioDetected)
            {
                return "--height: 25px;";
            }

            // Random height between 15 and 65 when audio is detected
            double height = random.Next(15, 75);
            return $"--height: {height}px;";
        }
        private async Task<bool> ToggleTeyaAIVisibility()
        {
            //bool access = await MemberService.HasProductAccess(Guid.Parse(User.id), Guid.Parse(Environment.GetEnvironmentVariable("ProductId"))); //add patient ID
            //return access;
            return true;
        }
        private async Task AddNewRecord()
        {
            try
            {
                TemplateData selectedTemplate;
                if (ProviderData != null)
                {
                    selectedTemplate = ProviderData.FirstOrDefault(t => t.IsDefault == true && t.VisitType == _PatientService.VisitType);
                    if (selectedTemplate == null)
                    {
                        var predefinedTemplates = await PredefinedTemplateService.GetTemplatesAsync();
                        selectedTemplate = predefinedTemplates.FirstOrDefault(t => t.VisitType == "Default");
                        if (selectedTemplate == null)
                        {
                            Snackbar.Add(Localizer["TemplateNotFound"], Severity.Error);
                            return;
                        }
                    }
                }
                else
                {
                    var predefinedTemplates = await PredefinedTemplateService.GetTemplatesAsync();
                    selectedTemplate = predefinedTemplates.FirstOrDefault(t => t.VisitType == "Default");
                    if (selectedTemplate == null)
                    {
                        Snackbar.Add(Localizer["TemplateNotFound"], Severity.Error);
                        return;
                    }
                }
                // Get the selected template
              

                // Parse the template JSON
                var templateStructure = JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, Dictionary<string, string>>>>(selectedTemplate.Template);

                // Transform template structure into record structure
                var recordNotes = new Dictionary<string, Dictionary<string, string>>();

                foreach (var section in templateStructure)
                {
                    var sectionName = section.Key;
                    var fields = section.Value;

                    var recordSection = new Dictionary<string, string>();

                    foreach (var field in fields)
                    {
                        // Initialize each field with empty content
                        recordSection[field.Key] = string.Empty;
                    }

                    recordNotes[sectionName] = recordSection;
                }

                // Create a new record with the transformed structure
                var newRecord = new Record
                {
                    Id = Guid.NewGuid(),
                    AppointmentId = appId,
                    PatientName = _PatientService.PatientData?.Name ?? "New Patient",
                    PatientId = _PatientService.PatientData?.Id ?? Guid.Empty,
                    PCPId = PcpID ?? Guid.Parse(User.id),
                    DateTime = DateTime.Now,
                    Notes = JsonSerializer.Serialize(recordNotes), // Use the transformed structure
                    isEditable = true,
                    OrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName),
                    Transcription = string.Empty,
                    WordTimings = null,
                    Template = selectedTemplate.Template,
                };

                // Save the new record
                var response = await ProgressNotesService.UploadRecordAsync(newRecord,OrgID,Subscription);
                if (response.IsSuccessStatusCode)
                {
                    Snackbar.Add(Localizer["RecordAddedSuccessfully"], Severity.Success);

                    // Refresh the records list

                    records = await ProgressNotesService.GetRecordsByPatientIdAsync(_PatientService.PatientData.Id, OrgID, Subscription);
                    records = records.Where(record => record.isEditable)
                                   .OrderByDescending(record => record.DateTime)
                                   .ToList();
                    record = records.FirstOrDefault();

                    componentRefreshKey = !componentRefreshKey;
                    StateHasChanged();
                }
                else
                {
                    Snackbar.Add(Localizer["ErrorAddingRecord"], Severity.Error);
                }
            }
            catch (Exception ex)
            {
                logger.LogError($"Error adding new record: {ex.Message}");
                Snackbar.Add(Localizer["ErrorAddingRecord"], Severity.Error);
            }
        }
        private string GetAudioUrl(Guid id)
        {
            return $"{Environment.GetEnvironmentVariable(Localizer["AudioUrl"])}/{id}.{Localizer["webm"]}";
        }

        private void HandleRichTextEditorChange(Dictionary<string, Dictionary<string, string>> data, string category, string noteKey, string newValue)
        {
            if (!data.ContainsKey(category))
            {
                data[category] = new Dictionary<string, string>();
            }
            data[category][noteKey] = newValue;
            hasChanges = true;
            StateHasChanged();
        }

        private string GetEditorContent(Record record, string sectionKey, string fieldKey)
        {
            try
            {
                var notesDict = JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, string>>>(record.Notes)
                    ?? new Dictionary<string, Dictionary<string, string>>();

                if (notesDict.ContainsKey(sectionKey) && notesDict[sectionKey].ContainsKey(fieldKey))
                {
                    return notesDict[sectionKey][fieldKey];
                }
                return string.Empty;
            }
            catch
            {
                return string.Empty;
            }
        }

        private int saveInterval { get; set; } = 500;

        private async Task HandleRichTextChange(Record record, string sectionKey, string fieldKey, string newValue)
  {
            var editorKey = $"{record.Id}_{sectionKey}_{fieldKey}";
            var notesDict = JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, string>>>(record.Notes)
                ?? new Dictionary<string, Dictionary<string, string>>();

            if (!notesDict.ContainsKey(sectionKey))
                notesDict[sectionKey] = new Dictionary<string, string>();
            notesDict[sectionKey][fieldKey] = newValue;
            record.Notes = JsonSerializer.Serialize(notesDict);
            hasChanges = true;
            editorContents[editorKey] = newValue;
            await InvokeAsync(StateHasChanged);
        }


        public bool IsReadOnly(Record record)
        {
            if (record.isEditable == true)
            {
                return false;
            }
            return true;
        }

      
        private async Task LockRecord(Record recorded)
        {
            // Check if record can be locked (must be signed and no pending cosign requests)
            var canLock = await CanLockRecord(recorded);
            if (!canLock)
            {
                Snackbar.Add("Record must be signed and have no pending cosign requests before locking.", Severity.Warning);
                return;
            }

            bool confirmed = await JS.InvokeAsync<bool>("confirm",
                "Are you sure you want to lock this record? You won't be able to make any changes afterward.");

            if (confirmed)
            {
                recorded.isEditable = false;
                selectedNoteId = Guid.Empty;
                var response = await ProgressNotesService.SaveRecordAsync(recorded, OrgID, Subscription);
                records = await ProgressNotesService.GetRecordsByPatientIdAsync(recorded.PatientId, OrgID, Subscription);
                records = records.Where(rec => rec.isEditable).OrderByDescending(rec => rec.DateTime).ToList();
                record = records.FirstOrDefault();
                componentRefreshKey = !componentRefreshKey;
                StateHasChanged();
            }
        }

        private async Task<bool> CanLockRecord(Record record)
        {
            try
            {
                return await SignatureService.CanLockRecordAsync(record.Id, OrgID, Subscription);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error checking if record can be locked");
                return false;
            }
        }

        private async Task OnSignatureChanged(bool isSigned)
        {
            // Refresh the component when signature status changes
            await InvokeAsync(StateHasChanged);
        }

        private async Task SaveRecord(Record recorded)
        {
            var response = await ProgressNotesService.SaveRecordAsync(recorded, OrgID, Subscription);
            records = await ProgressNotesService.GetRecordsByPatientIdAsync(recorded.PatientId, OrgID, Subscription);
            records = records.Where(rec => rec.isEditable).OrderByDescending(rec => rec.DateTime).ToList();
            record = records.FirstOrDefault();
            Snackbar.Add(Localizer["RecordSaved"], Severity.Success);
            StateHasChanged();
        }
        private async Task CreatePatient()
        {

            if (member.FirstName == null || member.FirstName == string.Empty || member.LastName == null || member.LastName == string.Empty || member.Email == null || member.Email == string.Empty || member.DateOfBirth == null || member.SSN == null || member.SSN == string.Empty)
            {
                Snackbar.Add(Localizer["Please fill in all the Fields"], Severity.Warning);
                return;
            }
            var emailValidationMessage = ValidateEmail(member.Email);
            var ssnValidationMessage = ValidateSSN(member.SSN);
            var firstnameValidateMessage = ValidateAlphabetic(member.FirstName);
            var lastnamevvalidationMessage = ValidateAlphabetic(member.LastName);
            var phonenumbervalidationMessage = ValidatePhoneNumber(member.PhoneNumber);

            member.MRN= GenerateUniqueMRN();

            if (!string.IsNullOrEmpty(emailValidationMessage))
            {
                Snackbar.Add(emailValidationMessage, Severity.Warning);
                return;
            }

            if (!string.IsNullOrEmpty(ssnValidationMessage))
            {
                Snackbar.Add(ssnValidationMessage, Severity.Warning);
                return;
            }

            if (!string.IsNullOrEmpty(firstnameValidateMessage))
            {
                Snackbar.Add(firstnameValidateMessage, Severity.Warning);
                return;
            }

            if (!string.IsNullOrEmpty(lastnamevvalidationMessage))
            {
                Snackbar.Add(lastnamevvalidationMessage, Severity.Warning);
                return;
            }
            if (!string.IsNullOrEmpty(phonenumbervalidationMessage))
            {
                Snackbar.Add(phonenumbervalidationMessage, Severity.Warning);
                return;
            }

            if (ssnExistsMember)
            {
                Snackbar.Add("Cannot create patient. SSN already exists or is invalid.", Severity.Error);
                return;
            }

            if (emailExists)
            {
                Snackbar.Add("Cannot create patient. Email is either invalid or already exists.", Severity.Error);
                return;
            }

            member.Id = Guid.NewGuid();
            member.IsActive = true;
            insurance.InsuranceId = Guid.NewGuid();
            insurance.OrganizationId = UserContext.ActiveUserOrganizationID;
            var insuranceAdded = await InsuranceService.AddInsuranceAsync(insurance);
            if (!insuranceAdded)
            {
                Logger.LogWarning(Localizer["ErrorAddingInsurance"]);
                return;
            }

            address.AddressId = Guid.NewGuid();
            address.OrganizationID = UserContext.ActiveUserOrganizationID;
            var addressAdded = await AddressService.AddAddressAsync(address);
            if (!addressAdded)
            {
                Logger.LogWarning(Localizer["ErrorAddingAddress"]);
                return;
            }

            guardian.GuardianId = Guid.NewGuid();
            guardian.OrganizationId = UserContext.ActiveUserOrganizationID;
            var GuardianAdded = await GuardianService.AddGuardianAsync(guardian);
            if (!GuardianAdded)
            {
                Logger.LogWarning(Localizer["ErrorAddingInsurance"]);
                return;
            }

            employer.EmployerId = Guid.NewGuid();
            employer.OrganizationId = UserContext.ActiveUserOrganizationID;
            var employerAdded = await EmployerService.AddEmployerAsync(employer);
            if (!employerAdded)
            {
                Logger.LogWarning(Localizer["ErrorAddingAddress"]);
                return;
            }
            member.UserName = member.FirstName + member.LastName;

            member.AddressId = address.AddressId;
            member.InsuranceId = insurance.InsuranceId;
            member.GuardianId = guardian.GuardianId;
            member.EmployerId = employer.EmployerId;
            member.RoleName = "Patient";
            member.OrganizationName = User.OrganizationName;
            member.OrganizationID = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
            var roles = await RoleService.GetAllRolesByOrgIdAsync(member.OrganizationID, Subscription);
            roles = roles.Where(role => role.RoleName == "Patient").ToList();
            member.RoleID = roles.FirstOrDefault()?.RoleId
                 ?? throw new Exception(Localizer["RoleNotFoundError"]);
            var registeredMember = await MemberService.RegisterMembersAsync(new List<Member> { member });


            var predefinedTemplates = await PredefinedTemplateService.GetTemplatesAsync();
            TemplateData selectedTemplate = predefinedTemplates.FirstOrDefault(t => t.VisitType == "Default");
            //TemplateData selectedTemplate = ProviderData.FirstOrDefault(t => t.IsDefault == true && t.VisitType == "Intervention Visit");
            var templateStructure = JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, Dictionary<string, string>>>>(selectedTemplate.Template);

            // Transform template structure into record structure
            var recordNotes = new Dictionary<string, Dictionary<string, string>>();

            foreach (var section in templateStructure)
            {
                var sectionName = section.Key;
                var fields = section.Value;

                var recordSection = new Dictionary<string, string>();

                foreach (var field in fields)
                {
                    // Initialize each field with empty content
                    recordSection[field.Key] = string.Empty;
                }

                recordNotes[sectionName] = recordSection;
            }

            var newRecord = new Record
            {
                Id = Guid.NewGuid(),
                PatientName = member.FirstName,
                PatientId = member.Id,
                PCPId = Guid.Parse(User.id),
                DateTime = DateTime.Now,
                Notes = JsonSerializer.Serialize(recordNotes),
                isEditable = true,
                OrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName),
                Transcription = string.Empty,
                WordTimings = null,
                Template = selectedTemplate.Template,
            };

            // Save the new record
            await ProgressNotesService.UploadRecordAsync(newRecord, OrgID, Subscription);

            // Refresh the records list
            records = await ProgressNotesService.GetRecordsByPCPIdAsync(Guid.Parse(User.id), OrgID, Subscription);
            records = records.Where(record => record.isEditable).OrderByDescending(record => record.DateTime).ToList();
            record = records.FirstOrDefault();
            showPatientCreationForm = false;
            PatientCreationFlag = false;

            member.FirstName = string.Empty;
            member.LastName = string.Empty;
            member.Email = string.Empty;
            member.SSN = string.Empty;
            member.PhoneNumber = string.Empty;
            member.DateOfBirth = null;
            Snackbar.Add(Localizer["Record Created Successfully"], Severity.Success);
            componentRefreshKey = !componentRefreshKey;
            await InvokeAsync(StateHasChanged);


        }
        //private async Task LockRecord()
        //{
        //    records[0].isEditable = false;
        //    records[0].Notes = JsonSerializer.Serialize(parsedNotes);
        //    var response = await ProgressNotesService.SaveRecordAsync(records[0], OrgID, Subscription);
        //    if (response.IsSuccessStatusCode)
        //    {
        //        records[0] = null;
        //    }


        //}

        Type GetComponentType(string componentName)
        {
            string fullTypeName = $"TeyaWebApp.Components.Pages.{componentName}";
            var assembly = Assembly.GetExecutingAssembly();
            return assembly.GetType(fullTypeName);

        }
        private void ShowMicrophone(Record record)
        {
            currentPatientId = record.PatientId;
            currentRecordId = record.Id;
            currentPatientName = record.PatientName;
            MicrophoneDialog.ShowAsync();
        }

        private async Task CloseMicrophoneDialog()
        {
            isRecorderActive = false;
            StopAnimation();
            StopCallDurationTimer();
            await JSRuntime.InvokeVoidAsync("BlazorAudioRecorder.CancelRecord");
            MicrophoneDialog.CloseAsync();
        }
        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["Backdrop-Disabled"], Severity.Info);
        }
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                await Task.Delay(int.Parse(Localizer["500"]));
                var accessToken = TokenService.AccessToken;
                await JSRuntime.InvokeVoidAsync(Localizer["BlazorAudioRecorder.Initialize"], DotNetObjectReference.Create(this));

                // Initialize height matching and content observation
                await JSRuntime.InvokeVoidAsync("matchContainerHeights");
                await JSRuntime.InvokeVoidAsync("observeNotesChanges");
            }
            else
            {
                // Re-match heights after each render
                await JSRuntime.InvokeVoidAsync("matchContainerHeights");
            }
        }
        private async Task OnMicIconClick()
        {
            isRecorderActive = !isRecorderActive;

            if (isRecorderActive)
            {
                StartAnimation();
                StartCallDurationTimer();
                await JSRuntime.InvokeVoidAsync(Localizer["BlazorAudioRecorder.StartRecord"]);
                await speechService.StartTranscriptionAsync(Guid.NewGuid());
            }
            else
            {
                isLoading = true; // Show loading icon
                StateHasChanged(); // Force UI update
                StopAnimation();
                StopCallDurationTimer();

                await JSRuntime.InvokeVoidAsync(Localizer["BlazorAudioRecorder.StopRecord"], currentRecordId, TokenService.AccessToken);

                //Guid id = Guid.NewGuid();
                //await JSRuntime.InvokeVoidAsync(Localizer["BlazorAudioRecorder.StopRecord"], id, TokenService.AccessToken);
                if (_PatientService.PatientData != null)
                {
                    await speechService.StopTranscriptionAsync(currentRecordId, _PatientService.PatientData.Id,currentPatientName,_PatientService.VisitType, OrgID, Subscription);
                    records = await ProgressNotesService.GetRecordsByPatientIdAsync(_PatientService.PatientData.Id, OrgID, Subscription);
                   
                }
                else
                {
                    await speechService.StopTranscriptionAsync(currentRecordId, currentPatientId, currentPatientName, _PatientService.VisitType, OrgID,Subscription);
                    records = await ProgressNotesService.GetRecordsByPCPIdAsync(Guid.Parse(User.id),OrgID,Subscription);
                }
                records = records.Where(record => record.isEditable).OrderByDescending(record => record.DateTime).ToList();
                record = records.FirstOrDefault();
                AICard = true;
                //for (int i = 0; i < records.Count; i++)
                //{
                //    parsedNotes[i] = JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, string>>>(
                //    JsonSerializer.Serialize(JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, string>>>(records[i].Notes)));
                //}
                isLoading = false; // Hide loading icon
                CloseMicrophoneDialog();
            }
            componentRefreshKey=!componentRefreshKey;
            StateHasChanged();
        }

      
    


        protected override void OnInitialized()
        {
            animationTimer = new System.Threading.Timer(UpdateBars, null, 0, 100);
        }
        public async Task OpenTreatmentDialogAsync()
        {
            try
            {
                await DialogService.ShowAsync<TreatmentPage>("Treatment", new DialogOptions { MaxWidth = MaxWidth.ExtraLarge, FullWidth = true, CloseOnEscapeKey = true, CloseButton = true });
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorOpeningDialog"]);
            }
        }

        private string GetRandomBarStyle()
        {
            if (isPaused)
            {
                int height = random.Next(15, 69);
                return $"--height: {height}px; --opacity: 0.5;";
            }

            int randomHeight = random.Next(32, 110);
            return $"--height: {randomHeight}px;";
        }
        private string GetNormalBarStyle()
        {
            int randomHeight = 25;
            return $"--height: {randomHeight}px;";
        }

        private void UpdateBars(object state)
        {
            if (!isPaused)
            {
                InvokeAsync(StateHasChanged);
            }
        }

        public void Dispose()
        {
            animationTimer?.Dispose();
        }

        [JSInvokable]
        public async Task OnRecordingComplete(string attempt)
        {
            Snackbar.Add($"✅ Upload complete on attempt #{attempt}", Severity.Success);
            await InvokeAsync(StateHasChanged);
        }

        [JSInvokable]
        public async Task OnRecordingError(string errorMessage)
        {
            Snackbar.Add($"❌ {errorMessage}", Severity.Error);
            await InvokeAsync(StateHasChanged);
        }

        private async Task OnPauseIconClick()
        {
            isPaused = !isPaused;
            if (isPaused)
            {
                PauseAnimation();
                PauseCallDurationTimer();
                await JSRuntime.InvokeVoidAsync(Localizer["BlazorAudioRecorder.PauseRecord"]);
            }
            else
            {
                ResumeAnimation();
                ResumeCallDurationTimer();
                await JSRuntime.InvokeVoidAsync(Localizer["BlazorAudioRecorder.ResumeRecord"]);
            }

            StateHasChanged();
        }
        [JSInvokable]
        public async Task ProcessAudioChunk(string base64AudioChunk)
        {
            await speechService.ProcessAudioChunk(base64AudioChunk);
        }
        private void StartAnimation()
        {
            isAnimating = true;
            isPaused = false;
            StateHasChanged();
        }

        private void StopAnimation()
        {
            isAnimating = false;
            StateHasChanged();
        }

        private void PauseAnimation()
        {
            isPaused = true;
            StopAnimation();
        }

        private void ResumeAnimation()
        {
            isPaused = false;
            StartAnimation();
        }

        private void StartCallDurationTimer()
        {
            callDuration = 0;
            callDurationTimer = new System.Timers.Timer(1000);
            callDurationTimer.Elapsed += UpdateCallDuration;
            callDurationTimer.Start();
        }

        private void UpdateCallDuration(object sender, ElapsedEventArgs e)
        {
            callDuration++;
            InvokeAsync(StateHasChanged);
        }

        private void StopCallDurationTimer()
        {
            callDurationTimer?.Stop();
            callDurationTimer?.Dispose();
            callDuration = 0;
            StateHasChanged();
        }

        private string GetBarAnimationStyle(int index) =>
            isAnimating ? $"animation: wave-lg {new Random().NextDouble() * (0.7 - 0.2) + 0.2}s infinite ease-in-out alternate;" : "";

        private void PauseCallDurationTimer()
        {
            callDurationTimer?.Stop();
        }

        private void ResumeCallDurationTimer()
        {
            callDurationTimer?.Start();
        }

        private string FormatCallDuration(int durationInSeconds)
        {
            TimeSpan time = TimeSpan.FromSeconds(durationInSeconds);
            return time.ToString(@"mm\:ss");
        }



        public void OpenCustomImmunizationAlertDialogAsync()
        {
            try
            {
                var options = new DialogOptions
                {
                    MaxWidth = MaxWidth.Large,
                    FullWidth = true,
                    CloseOnEscapeKey = true,
                    CloseButton = true
                };

                DialogService.Show<CustomImmunizationAlert>("Immunization Alerts", options);

                // The dialog will be closed by the CustomLabAlert component when Save or Cancel is clicked
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorOpeningDialog"]);
            }
        }

        
        private async Task GoBackToEncounters()
        {


            if (OnGoBackToEncounters.HasDelegate)
            {
                await OnGoBackToEncounters.InvokeAsync();
            }
            if (OnClearSelectedNoteId.HasDelegate)
            {
                await OnClearSelectedNoteId.InvokeAsync();
            }
            StateHasChanged();

        }

        private string GetInitials(string name)
        {
            if (string.IsNullOrWhiteSpace(name)) return "NA";
            var parts = name.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            return string.Join("", parts.Take(2).Select(p => p[0])).ToUpper();
        }

        private void ShowPatientCreationForm()
        {
            showPatientCreationForm = !showPatientCreationForm;
            // Ensure PatientCreationFlag is true to show the form
            if (showPatientCreationForm)
            {
                PatientCreationFlag = true;
            }
            StateHasChanged();
        }

        private void FormatSSN()
        {
            if (!string.IsNullOrWhiteSpace(member.SSN))
            {
                // Remove all non-digit characters
                string digitsOnly = new string(member.SSN.Where(char.IsDigit).ToArray());

                // If exactly 9 digits, format it
                if (digitsOnly.Length == 9)
                {
                    member.SSN = $"{digitsOnly.Substring(0, 3)}-{digitsOnly.Substring(3, 2)}-{digitsOnly.Substring(5, 4)}";
                    StateHasChanged(); // Force UI update
                }
            }
        }

        private string? ValidateSSN(string? value)
        {
            string? result = null;
            if (!string.IsNullOrWhiteSpace(value))
            {
                value = value.Trim();
                string digitsOnly = new string(value.Where(char.IsDigit).ToArray());
                if (digitsOnly.Length != 9)
                {
                    result = "SSN must be 9 digits (e.g., ***********).";
                }
            }
            return result;
        }


        private string? ValidateEmail(string? value)
        {
            string? result = null;

            if (!string.IsNullOrWhiteSpace(value))
            {
                value = value.Trim();
                try
                {
                    var addr = new System.Net.Mail.MailAddress(value);
                }
                catch
                {
                    return "Invalid email (e.g., <EMAIL>).";
                }
            }

            return result;
        }

        private string? ValidateAlphabetic(string? value)
        {
            string? result = null;

            if (!string.IsNullOrWhiteSpace(value))
            {
                value = value.Trim();
                if (!value.All(c => char.IsLetter(c) || c == ' ' || c == '.'))
                {
                    result = "Invalid Name.";
                }
            }

            return result;
        }


        private string? ValidatePhoneNumber(string? value)
        {
            string? result = null;

            if (!string.IsNullOrWhiteSpace(value))
            {
                value = value.Trim();

                if (value.Any(char.IsLetter))
                {
                    result = Localizer["Phone number must not contain letters."];
                }
                else
                {
                    string digitsOnly = new string(value.Where(char.IsDigit).ToArray());

                    if (digitsOnly.Length != 10)
                    {
                        result = Localizer["Phone number must be 10 digits (e.g., ************)."];
                    }
                }
            }

            return result;
        }




        public PatternMask ssnMask = new PatternMask("###-##-####")
        {
            MaskChars = new[] { new MaskChar('#', @"[0-9]") }
        };



        bool ssnExistsMember = false;
        string ssnErrorTextMember = string.Empty;
        bool ssnExistsGuardian = false;
        string ssnErrorTextGuardian = string.Empty;
        bool emailExists = false;
        string emailErrorText = string.Empty;


        private async Task ValidateAndCheckEmailAsync()
        {
            if (string.IsNullOrWhiteSpace(member.Email))
            {
                emailExists = false;
                emailErrorText = string.Empty;
                return;
            }

            var email = member.Email.Trim();

            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
            }
            catch
            {
                emailExists = true;
                emailErrorText = "Invalid email format.e.g <EMAIL>";
                return;
            }

            var tldRegex = new Regex(@"^[^@\s]+@[^@\s]+\.(com|org|net|edu|gov|in|us|co|io|dev)$", RegexOptions.IgnoreCase);
            if (!tldRegex.IsMatch(email))
            {
                emailExists = true;
                emailErrorText = "Invalid email domain. Allowed: .com,.in,.us,.co,.io,.dev...";
                return;
            }


            emailExists = await DoesEmailExistAsync(email, member.Id);

            // Check if guardian email is the same as member email
            if (!string.IsNullOrWhiteSpace(email) &&
                !string.IsNullOrWhiteSpace(guardian.GuardianEmail) &&
                email.Equals(guardian.GuardianEmail.Trim(), StringComparison.OrdinalIgnoreCase))
            {
                emailExists = true;
                emailErrorText = "Guardian email cannot be the same as the Member email.";
                Snackbar.Add(emailErrorText, Severity.Error);
                return;
            }
            emailErrorText = emailExists ? "Email already exists." : string.Empty;



        }

        /// <summary>
        /// Check the Email address is already exist or not
        /// </summary>
        /// <param name="email"></param>
        /// <param name="currentMemberId"></param>
        /// <returns></returns>
        public Task<bool> DoesEmailExistAsync(string email, Guid currentMemberId)
        {
            var exists = AllOrgMembers.Any(m =>
                m.Id != currentMemberId &&
                !string.IsNullOrWhiteSpace(m.Email) &&
                m.Email.Trim().Equals(email.Trim(), StringComparison.OrdinalIgnoreCase));

            return Task.FromResult(exists);
        }

        private async Task ValidateAndCheckSSNAsync()
        {
            ssnErrorTextMember = string.Empty;
            ssnExistsMember = false;

            if (string.IsNullOrWhiteSpace(member.SSN))
                return;

            var digitsOnly = new string(member.SSN.Where(char.IsDigit).ToArray());

            if (digitsOnly.Length != 9)
            {
                ssnErrorTextMember = "SSN must be exactly 9 digits.";
                ssnExistsMember = true;
                return;
            }

            if (digitsOnly == "000000000" ||
                digitsOnly.StartsWith("000") ||
                digitsOnly.Substring(3, 2) == "00" ||
                digitsOnly.Substring(5) == "0000")
            {
                ssnErrorTextMember = "Invalid SSN pattern.";
                ssnExistsMember = true;
                return;
            }

            member.SSN = $"{digitsOnly[..3]}-{digitsOnly.Substring(3, 2)}-{digitsOnly[5..]}";

            if (!string.IsNullOrWhiteSpace(member.SSN) && OrgID != null)
            {
                ssnExistsMember = await DoesSSNExistAsync(member.SSN, OrgID, false, member.Id);
                if (ssnExistsMember)
                {
                    ssnErrorTextMember = "SSN already exists.";
                }
            }

            StateHasChanged();
        }


        public async Task<bool> DoesSSNExistAsync(string ssn, Guid? orgId, bool subscription, Guid? currentMemberId = null)
        {
            if (string.IsNullOrWhiteSpace(ssn) || orgId == null)
                return false;


            
            var normalizedSSN = ssn.Replace("-", "").Trim();

            return AllOrgMembers.Any(m =>
                m.Id != currentMemberId &&
                !string.IsNullOrWhiteSpace(m.SSN) &&
                m.SSN.Replace("-", "").Trim() == normalizedSSN);
        }

        private MudDatePicker? dobDatePicker;
        private async Task CloseDatePicker(MudDatePicker picker)
        {
            if (picker is not null)
            {
                await picker.ClearAsync();
                await picker.CloseAsync();
            }
        }

        /// <summary>
        /// Generate new MRN number for Patient.Incremental
        /// </summary>
        /// <returns></returns>
        public string GenerateUniqueMRN()
        {
            int maxMRN = GetMaxMRN();
            int nextMRN = maxMRN + 1;

            return nextMRN.ToString($"D{MRNLength}"); // e.g., "**********"
        }

        /// <summary>
        /// Fetch the max MRN number from the Database
        /// </summary>
        /// <returns></returns>
        private int GetMaxMRN()
        {
            int maxMRN = 0;
            var members = AllOrgMembers;

            foreach (var member in members)
            {
                if (!string.IsNullOrWhiteSpace(member.MRN) && int.TryParse(member.MRN, out int parsedMRN))
                {
                    if (parsedMRN > maxMRN)
                        maxMRN = parsedMRN;
                }
            }

            return maxMRN;
        }
    }
}   
